{"data_mtime": 1754415047, "dep_lines": [8, 6, 7, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.responses", "openai._utils", "openai._compat", "__future__", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "openai._models", "openai.types", "openai.types.responses.parsed_response", "openai.types.responses.response", "openai.types.responses.response_completed_event", "openai.types.responses.response_function_call_arguments_delta_event", "openai.types.responses.response_text_delta_event", "openai.types.responses.response_text_done_event", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "c79f5ef3ee16a3d3c20438d41af3355665405cd8", "id": "openai.lib.streaming.responses._events", "ignore_all": true, "interface_hash": "85e417d516193444e1f4aed0fb391b2f6d8bfaf8", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_events.py", "plugin_data": null, "size": 5285, "suppressed": [], "version_id": "1.17.1"}