{"data_mtime": 1754415052, "dep_lines": [24, 23, 24, 23, 15, 17, 18, 19, 20, 21, 1, 1], "dep_prios": [10, 10, 20, 20, 5, 10, 10, 10, 5, 5, 5, 30], "dependencies": ["google.generativeai.types.citation_types", "google.generativeai.string_utils", "google.generativeai.types", "google.generativeai", "__future__", "sys", "abc", "dataclasses", "typing", "typing_extensions", "builtins", "_frozen_importlib"], "hash": "1bf88c757cca4083b0fc9e3b81d2845362f9b8ad", "id": "google.generativeai.types.text_types", "ignore_all": true, "interface_hash": "589f90d0f2fab62cea3acc715e85d5a6653d67b0", "mtime": 1754404370, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\types\\text_types.py", "plugin_data": null, "size": 982, "suppressed": [], "version_id": "1.17.1"}