{"data_mtime": 1754415335, "dep_lines": [34, 27, 21, 22, 23, 24, 25, 26, 19, 21, 26, 17, 19, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 29, 33], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 10, 10, 20, 20, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["google.api_core.operations_v1.transports.base", "google.auth.transport.requests", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.path_template", "google.api_core.rest_helpers", "google.api_core.retry", "google.auth.credentials", "requests.__version__", "google.api_core", "google.auth", "typing", "requests", "google", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth.transport", "requests.sessions"], "hash": "1c0aa2a8da064929356ece5d3278db5100015a29", "id": "google.api_core.operations_v1.transports.rest", "ignore_all": true, "interface_hash": "6d076e48d6a780a417faea325ac5b4d8ef20e565", "mtime": 1752463755, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\rest.py", "plugin_data": null, "size": 20599, "suppressed": ["google.longrunning", "google.protobuf", "grpc"], "version_id": "1.17.1"}