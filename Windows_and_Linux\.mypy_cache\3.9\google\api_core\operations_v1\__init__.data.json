{".class": "MypyFile", "_fullname": "google.api_core.operations_v1", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractOperationsClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.abstract_operations_client.AbstractOperationsClient", "kind": "Gdef"}, "AsyncOperationsRestClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "kind": "Gdef"}, "AsyncOperationsRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.transports.rest_asyncio.AsyncOperationsRestTransport", "kind": "Gdef"}, "OperationsAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_async_client.OperationsAsyncClient", "kind": "Gdef"}, "OperationsClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_client.OperationsClient", "kind": "Gdef"}, "OperationsRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.transports.rest.OperationsRestTransport", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.api_core.operations_v1.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\api_core\\operations_v1\\__init__.py"}