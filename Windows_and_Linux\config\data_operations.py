"""
Writing Tools - Data Operations Module
Contains all functions for creating, modifying, and manipulating configuration data
"""

from typing import Dict

from .constants import (
    DEFAULT_ACTIONS_VALUES,
    DEFAULT_MODELS,
    DEFAULT_SYSTEM_VALUES,
    PROVIDER_DISPLAY_NAMES,
)
from .interfaces import ActionConfig, SystemConfig, UnifiedSettings


def get_default_model_for_provider(provider: str) -> str:
    """Get the default model for a given provider"""
    return DEFAULT_MODELS.get(provider, "")


def get_provider_display_name(provider: str) -> str:
    """Get the display name for a provider"""
    return PROVIDER_DISPLAY_NAMES.get(provider, provider)


def get_provider_internal_name(display_name: str) -> str:
    """Get the internal name from display name"""
    return next(
        (internal for internal, display in PROVIDER_DISPLAY_NAMES.items() if display == display_name),
        display_name,
    )


def create_default_system_config() -> SystemConfig:
    """Create a fresh SystemConfig instance with default values"""
    return SystemConfig(**DEFAULT_SYSTEM_VALUES.__dict__)


def create_default_actions_config() -> Dict[str, ActionConfig]:
    """Create a dictionary of ActionConfig instances from default values"""
    return {name: ActionConfig(**values.__dict__) for name, values in DEFAULT_ACTIONS_VALUES.items()}


def create_default_settings() -> UnifiedSettings:
    """Create a complete UnifiedSettings instance with all default configurations"""
    return UnifiedSettings(
        system=create_default_system_config(),
        actions=create_default_actions_config(),
        custom_data={},
    )


def merge_system_data(
    user_data: Dict[str, SystemConfig],
    default_values: Dict[str, SystemConfig],
) -> Dict[str, SystemConfig]:
    """Merge user system data with default values, filtering out invalid fields"""
    result = {**default_values}

    if not user_data:
        return result

    if isinstance(user_data, dict):
        # Only merge fields that exist in default_values (valid SystemConfig fields)
        for key, value in user_data.items():
            if key in default_values:
                result[key] = value

    return result


def merge_actions_data(
    user_data: Dict[str, ActionConfig],
    default_values: Dict[str, ActionConfig],
) -> Dict[str, ActionConfig]:
    """Merge user actions data with default values and create ActionConfig instances"""
    merged_values = default_values.copy()

    if not user_data:
        return merged_values

    if isinstance(user_data, dict):
        merged_values |= user_data

    # Convert to ActionConfig instances
    return {name: ActionConfig(**values.__dict__) for name, values in merged_values.items()}


def create_unified_settings_from_data(user_data) -> UnifiedSettings:
    """
    Create UnifiedSettings from user data, merging with defaults.
    Avoids double instantiation by working with raw values.
    """

    system_user_data = user_data.get("system", {})
    if not isinstance(system_user_data, dict):
        system_user_data = {}
    system_data = merge_system_data(system_user_data, DEFAULT_SYSTEM_VALUES.__dict__)

    actions_data = merge_actions_data(user_data.get("actions"), DEFAULT_ACTIONS_VALUES.__dict__)

    custom_data = user_data.get("custom_data", {})

    return UnifiedSettings(
        system=SystemConfig(**system_data.__dict__),
        actions=actions_data,
        custom_data=custom_data,
    )
