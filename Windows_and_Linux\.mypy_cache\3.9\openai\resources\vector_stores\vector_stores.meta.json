{"data_mtime": 1754415048, "dep_lines": [11, 32, 43, 19, 19, 19, 19, 41, 42, 44, 45, 10, 19, 26, 27, 28, 29, 30, 31, 40, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.vector_stores.files", "openai.resources.vector_stores.file_batches", "openai.types.shared_params.metadata", "openai.types.vector_store_list_params", "openai.types.vector_store_create_params", "openai.types.vector_store_search_params", "openai.types.vector_store_update_params", "openai.types.vector_store", "openai.types.vector_store_deleted", "openai.types.file_chunking_strategy_param", "openai.types.vector_store_search_response", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types.auto_file_chunking_strategy_param", "openai.types.shared_params", "openai.types.shared_params.comparison_filter", "openai.types.shared_params.compound_filter", "openai.types.static_file_chunking_strategy_object_param", "openai.types.static_file_chunking_strategy_param", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "f59e8fde0a90dd35501d8f532d03cd5ec10c9434", "id": "openai.resources.vector_stores.vector_stores", "ignore_all": true, "interface_hash": "22211a61fa10136112aa27ce67d8971479c73614", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\vector_stores\\vector_stores.py", "plugin_data": null, "size": 35249, "suppressed": [], "version_id": "1.17.1"}