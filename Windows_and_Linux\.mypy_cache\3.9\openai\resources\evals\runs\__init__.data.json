{".class": "MypyFile", "_fullname": "openai.resources.evals.runs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncOutputItems": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.output_items.AsyncOutputItems", "kind": "Gdef"}, "AsyncOutputItemsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.output_items.AsyncOutputItemsWithRawResponse", "kind": "Gdef"}, "AsyncOutputItemsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.output_items.AsyncOutputItemsWithStreamingResponse", "kind": "Gdef"}, "AsyncRuns": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRuns", "kind": "Gdef"}, "AsyncRunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRunsWithRawResponse", "kind": "Gdef"}, "AsyncRunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRunsWithStreamingResponse", "kind": "Gdef"}, "OutputItems": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.output_items.OutputItems", "kind": "Gdef"}, "OutputItemsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.output_items.OutputItemsWithRawResponse", "kind": "Gdef"}, "OutputItemsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.output_items.OutputItemsWithStreamingResponse", "kind": "Gdef"}, "Runs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.Runs", "kind": "Gdef"}, "RunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.RunsWithRawResponse", "kind": "Gdef"}, "RunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.RunsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.evals.runs.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.runs.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\evals\\runs\\__init__.py"}