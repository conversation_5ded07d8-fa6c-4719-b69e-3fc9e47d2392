{".class": "MypyFile", "_fullname": "google.generativeai.types.generation_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASYNC_GENERATE_CONTENT_RESPONSE_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.ASYNC_GENERATE_CONTENT_RESPONSE_DOC", "name": "ASYNC_GENERATE_CONTENT_RESPONSE_DOC", "setter_type": null, "type": "builtins.str"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncGenerateContentResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse", "name": "AsyncGenerateContentResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.AsyncGenerateContentResponse", "google.generativeai.types.generation_types.BaseGenerateContentResponse", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.__aiter__", "name": "__aiter__", "type": null}}, "from_aiterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "iterator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.from_aiterator", "name": "from_aiterator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "iterator"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.AsyncGenerateContentResponse"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_aiterator of AsyncGenerateContentResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.from_aiterator", "name": "from_aiterator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "iterator"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.AsyncGenerateContentResponse"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_aiterator of AsyncGenerateContentResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.from_response", "name": "from_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.AsyncGenerateContentResponse"}, "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_response of AsyncGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.from_response", "name": "from_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.AsyncGenerateContentResponse"}, "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_response of AsyncGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.resolve", "name": "resolve", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.AsyncGenerateContentResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.AsyncGenerateContentResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncIterable": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterable", "kind": "Gdef", "module_public": false}, "BaseGenerateContentResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse", "name": "BaseGenerateContentResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.BaseGenerateContentResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "done", "iterator", "result", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "done", "iterator", "result", "chunks"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}], "uses_pep604_syntax": true}, "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse", {".class": "UnionType", "items": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseGenerateContentResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of BaseGenerateContentResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_chunks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse._chunks", "name": "_chunks", "setter_type": null, "type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse._done", "name": "_done", "setter_type": null, "type": "builtins.bool"}}, "_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse._error", "name": "_error", "setter_type": null, "type": "google.generativeai.types.generation_types.BlockedPromptException"}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}], "uses_pep604_syntax": false}}}, "_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse._result", "name": "_result", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"}}, "candidates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.candidates", "name": "candidates", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.candidates", "name": "candidates", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "candidates of BaseGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.model_version", "name": "model_version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.model_version", "name": "model_version", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_version of BaseGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.parts", "name": "parts", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.parts", "name": "parts", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parts of BaseGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prompt_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.prompt_feedback", "name": "prompt_feedback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.prompt_feedback", "name": "prompt_feedback", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prompt_feedback of BaseGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.text", "name": "text", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.text", "name": "text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of BaseGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.to_dict", "name": "to_dict", "type": null}}, "usage_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.usage_metadata", "name": "usage_metadata", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.usage_metadata", "name": "usage_metadata", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "usage_metadata of BaseGenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.BaseGenerateContentResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.BaseGenerateContentResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlockedPromptException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.BlockedPromptException", "name": "BlockedPromptException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.BlockedPromptException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.BlockedPromptException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.BlockedPromptException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.BlockedPromptException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BrokenResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.BrokenResponseError", "name": "BrokenResponseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.BrokenResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.BrokenResponseError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.BrokenResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.BrokenResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GENERATE_CONTENT_RESPONSE_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GENERATE_CONTENT_RESPONSE_DOC", "name": "GENERATE_CONTENT_RESPONSE_DOC", "setter_type": null, "type": "builtins.str"}}, "GenerateContentResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.generativeai.types.generation_types.BaseGenerateContentResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.GenerateContentResponse", "name": "GenerateContentResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.GenerateContentResponse", "google.generativeai.types.generation_types.BaseGenerateContentResponse", "builtins.object"], "names": {".class": "SymbolTable", "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.__iter__", "name": "__iter__", "type": null}}, "from_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "iterator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.from_iterator", "name": "from_iterator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "iterator"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.GenerateContentResponse"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_iterator of GenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.from_iterator", "name": "from_iterator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "iterator"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.GenerateContentResponse"}, {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_iterator of GenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.from_response", "name": "from_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.GenerateContentResponse"}, "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_response of GenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.from_response", "name": "from_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.generation_types.GenerateContentResponse"}, "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_response of GenerateContentResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.resolve", "name": "resolve", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.GenerateContentResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.GenerateContentResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GenerationConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.GenerationConfig", "name": "GenerationConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.GenerationConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 166, "name": "candidate_count", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 167, "name": "stop_sequences", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 168, "name": "max_output_tokens", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 169, "name": "temperature", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 170, "name": "top_p", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 171, "name": "top_k", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 172, "name": "response_mime_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 173, "name": "response_schema", "type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 174, "name": "presence_penalty", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 175, "name": "frequency_penalty", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.GenerationConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "candidate_count", "stop_sequences", "max_output_tokens", "temperature", "top_p", "top_k", "response_mime_type", "response_schema", "presence_penalty", "frequency_penalty"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.GenerationConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "candidate_count", "stop_sequences", "max_output_tokens", "temperature", "top_p", "top_k", "response_mime_type", "response_schema", "presence_penalty", "frequency_penalty"], "arg_types": ["google.generativeai.types.generation_types.GenerationConfig", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of GenerationConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["candidate_count", "stop_sequences", "max_output_tokens", "temperature", "top_p", "top_k", "response_mime_type", "response_schema", "presence_penalty", "frequency_penalty"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["candidate_count", "stop_sequences", "max_output_tokens", "temperature", "top_p", "top_k", "response_mime_type", "response_schema", "presence_penalty", "frequency_penalty"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of GenerationConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["candidate_count", "stop_sequences", "max_output_tokens", "temperature", "top_p", "top_k", "response_mime_type", "response_schema", "presence_penalty", "frequency_penalty"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of GenerationConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "candidate_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.candidate_count", "name": "candidate_count", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "frequency_penalty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.frequency_penalty", "name": "frequency_penalty", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "max_output_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.max_output_tokens", "name": "max_output_tokens", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "presence_penalty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.presence_penalty", "name": "presence_penalty", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "response_mime_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.response_mime_type", "name": "response_mime_type", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "response_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.response_schema", "name": "response_schema", "setter_type": null, "type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "stop_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.stop_sequences", "name": "stop_sequences", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "temperature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.temperature", "name": "temperature", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "top_k": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.top_k", "name": "top_k", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "top_p": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.GenerationConfig.top_p", "name": "top_p", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.GenerationConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.GenerationConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GenerationConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.GenerationConfigDict", "name": "GenerationConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.GenerationConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.GenerationConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["candidate_count", "builtins.int"], ["stop_sequences", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], ["max_output_tokens", "builtins.int"], ["temperature", "builtins.float"], ["response_mime_type", "builtins.str"], ["response_schema", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": true}], ["presence_penalty", "builtins.float"], ["frequency_penalty", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "GenerationConfigType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.generation_types.GenerationConfigType", "line": 178, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigDict"}, "google.generativeai.types.generation_types.GenerationConfig"], "uses_pep604_syntax": false}}}, "IncompleteIterationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.IncompleteIterationError", "name": "IncompleteIterationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.IncompleteIterationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.IncompleteIterationError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.IncompleteIterationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.IncompleteIterationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "StopCandidateException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.generation_types.StopCandidateException", "name": "StopCandidateException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.StopCandidateException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.generation_types", "mro": ["google.generativeai.types.generation_types.StopCandidateException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.generation_types.StopCandidateException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.generation_types.StopCandidateException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_INCOMPLETE_ITERATION_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types._INCOMPLETE_ITERATION_MESSAGE", "name": "_INCOMPLETE_ITERATION_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.generation_types.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.generation_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.generation_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.generation_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.generation_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.generation_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.generation_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_join_candidate_lists": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["candidate_lists"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_candidate_lists", "name": "_join_candidate_lists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["candidate_lists"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.Candidate"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_candidate_lists", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_join_candidates": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["candidates"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_candidates", "name": "_join_candidates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["candidates"], "arg_types": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.Candidate"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_candidates", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_join_chunks": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_chunks", "name": "_join_chunks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chunks"], "arg_types": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_chunks", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_join_citation_metadatas": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["citation_metadatas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_citation_metadatas", "name": "_join_citation_metadatas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["citation_metadatas"], "arg_types": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.citation.CitationMetadata"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_citation_metadatas", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_join_code_execution_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["result_1", "result_2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_code_execution_result", "name": "_join_code_execution_result", "type": null}}, "_join_contents": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_contents", "name": "_join_contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["contents"], "arg_types": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_contents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_join_executable_code": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["code_1", "code_2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_executable_code", "name": "_join_executable_code", "type": null}}, "_join_prompt_feedbacks": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["prompt_feedbacks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_prompt_feedbacks", "name": "_join_prompt_feedbacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["prompt_feedbacks"], "arg_types": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse.PromptFeedback"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_prompt_feedbacks", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_join_safety_ratings_lists": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["safety_ratings_lists"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._join_safety_ratings_lists", "name": "_join_safety_ratings_lists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["safety_ratings_lists"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.safety.SafetyRating"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_join_safety_ratings_lists", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_schema": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["generation_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types._normalize_schema", "name": "_normalize_schema", "type": null}}, "_rename_schema_fields": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.responder._rename_schema_fields", "kind": "Gdef", "module_public": false}, "aiter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.aiter", "name": "aiter", "type": null}}, "anext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.generativeai.types.generation_types.anext", "name": "anext", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "content_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef", "module_public": false}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef", "module_public": false}, "rewrite_stream_error": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "google.generativeai.types.generation_types.rewrite_stream_error", "name": "rewrite_stream_error", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.types.generation_types.rewrite_stream_error", "name": "rewrite_stream_error", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rewrite_stream_error", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "string_utils": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.string_utils", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef", "module_public": false}, "to_generation_config_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["generation_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.generation_types.to_generation_config_dict", "name": "to_generation_config_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["generation_config"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_generation_config_dict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\types\\generation_types.py"}