{"data_mtime": 1754415047, "dep_lines": [5, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.chat", "__future__", "typing_extensions", "builtins", "_frozen_importlib", "abc", "openai._models", "openai.types", "openai.types.chat.chat_completion", "openai.types.chat.chat_completion_audio", "openai.types.chat.chat_completion_message", "openai.types.chat.chat_completion_message_tool_call", "openai.types.chat.parsed_chat_completion", "openai.types.chat.parsed_function_tool_call", "openai.types.completion_usage", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "typing"], "hash": "56d627177c256bca4c8bf453aa81c98126c77838", "id": "openai.lib.streaming.chat._types", "ignore_all": true, "interface_hash": "b1784df7bf85729ce3a3279bd45c7b21ca06601c", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_types.py", "plugin_data": null, "size": 739, "suppressed": [], "version_id": "1.17.1"}