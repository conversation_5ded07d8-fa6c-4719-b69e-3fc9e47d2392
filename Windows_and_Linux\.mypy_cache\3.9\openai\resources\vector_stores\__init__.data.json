{".class": "MypyFile", "_fullname": "openai.resources.vector_stores", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncFileBatches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.file_batches.AsyncFileBatches", "kind": "Gdef"}, "AsyncFileBatchesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.file_batches.AsyncFileBatchesWithRawResponse", "kind": "Gdef"}, "AsyncFileBatchesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.file_batches.AsyncFileBatchesWithStreamingResponse", "kind": "Gdef"}, "AsyncFiles": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.files.AsyncFiles", "kind": "Gdef"}, "AsyncFilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "kind": "Gdef"}, "AsyncFilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "kind": "Gdef"}, "AsyncVectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStores", "kind": "Gdef"}, "AsyncVectorStoresWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithRawResponse", "kind": "Gdef"}, "AsyncVectorStoresWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithStreamingResponse", "kind": "Gdef"}, "FileBatches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.file_batches.FileBatches", "kind": "Gdef"}, "FileBatchesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.file_batches.FileBatchesWithRawResponse", "kind": "Gdef"}, "FileBatchesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.file_batches.FileBatchesWithStreamingResponse", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.files.Files", "kind": "Gdef"}, "FilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.files.FilesWithRawResponse", "kind": "Gdef"}, "FilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.files.FilesWithStreamingResponse", "kind": "Gdef"}, "VectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStores", "kind": "Gdef"}, "VectorStoresWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStoresWithRawResponse", "kind": "Gdef"}, "VectorStoresWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStoresWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.vector_stores.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\vector_stores\\__init__.py"}