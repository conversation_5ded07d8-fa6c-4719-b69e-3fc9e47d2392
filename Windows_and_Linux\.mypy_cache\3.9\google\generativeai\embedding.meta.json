{"data_mtime": 1754415052, "dep_lines": [26, 27, 28, 29, 20, 21, 23, 26, 20, 21, 15, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 20, 20, 20, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.generativeai.types.helper_types", "google.generativeai.types.model_types", "google.generativeai.types.text_types", "google.generativeai.types.content_types", "google.ai.generativelanguage", "google.generativeai.protos", "google.generativeai.client", "google.generativeai.types", "google.ai", "google.generativeai", "__future__", "itertools", "typing", "google", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.generative_service.async_client", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.file", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.model", "google.api_core", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.api_core.timeout", "google.generativeai.types.file_types", "types"], "hash": "44b17f029738cd2ebb8616f8c51d5a5cb4a7736d", "id": "google.generativeai.embedding", "ignore_all": true, "interface_hash": "9ee588b32f0f6530b12d001e1f71ba6bcbfa25f2", "mtime": 1754404370, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\embedding.py", "plugin_data": null, "size": 12102, "suppressed": [], "version_id": "1.17.1"}