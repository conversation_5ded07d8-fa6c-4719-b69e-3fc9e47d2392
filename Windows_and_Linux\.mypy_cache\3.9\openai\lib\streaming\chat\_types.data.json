{".class": "MypyFile", "_fullname": "openai.lib.streaming.chat._types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ParsedChatCompletion": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion", "kind": "Gdef"}, "ParsedChatCompletionMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletionMessage", "kind": "Gdef"}, "ParsedChatCompletionMessageSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.lib.streaming.chat._types.ParsedChatCompletionMessageSnapshot", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletionMessage"}}}, "ParsedChatCompletionSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot", "line": 7, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion"}}}, "ParsedChoice": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChoice", "kind": "Gdef"}, "ParsedChoiceSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.lib.streaming.chat._types.ParsedChoiceSnapshot", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChoice"}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_types.py"}