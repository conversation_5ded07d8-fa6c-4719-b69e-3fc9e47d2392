{".class": "MypyFile", "_fullname": "openai.types.vector_stores", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileBatchCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_batch_create_params.FileBatchCreateParams", "kind": "Gdef"}, "FileBatchListFilesParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_batch_list_files_params.FileBatchListFilesParams", "kind": "Gdef"}, "FileContentResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_content_response.FileContentResponse", "kind": "Gdef"}, "FileCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_create_params.FileCreateParams", "kind": "Gdef"}, "FileListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_list_params.FileListParams", "kind": "Gdef"}, "FileUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_update_params.FileUpdateParams", "kind": "Gdef"}, "VectorStoreFile": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "kind": "Gdef"}, "VectorStoreFileBatch": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.vector_store_file_batch.VectorStoreFileBatch", "kind": "Gdef"}, "VectorStoreFileDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.vector_stores.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\vector_stores\\__init__.py"}