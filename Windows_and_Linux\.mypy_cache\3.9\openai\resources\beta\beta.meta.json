{"data_mtime": 1754415048, "dep_lines": [15, 24, 6, 23, 5, 14, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.threads.threads", "openai.resources.beta.realtime.realtime", "openai.resources.beta.assistants", "openai.resources.chat", "openai._compat", "openai._resource", "__future__", "builtins", "_frozen_importlib", "abc", "openai.resources.beta.realtime", "openai.resources.beta.threads", "openai.resources.chat.chat", "typing"], "hash": "dec9a251cab9944aa5ea8289083317db18ac0c06", "id": "openai.resources.beta.beta", "ignore_all": true, "interface_hash": "82cf44da64a5c7129992f0d9c99c03c6cd9743db", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\beta\\beta.py", "plugin_data": null, "size": 5485, "suppressed": [], "version_id": "1.17.1"}