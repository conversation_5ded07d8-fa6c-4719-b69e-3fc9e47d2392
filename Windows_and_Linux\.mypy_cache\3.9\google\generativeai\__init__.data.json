{".class": "MypyFile", "_fullname": "google.generativeai", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChatSession": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.generative_models.ChatSession", "kind": "Gdef"}, "GenerationConfig": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.GenerationConfig", "kind": "Gdef"}, "GenerativeModel": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.generative_models.GenerativeModel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "caching": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.caching", "kind": "Gdef"}, "configure": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.configure", "kind": "Gdef"}, "create_tuned_model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.create_tuned_model", "kind": "Gdef"}, "delete_file": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.files.delete_file", "kind": "Gdef"}, "delete_tuned_model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.delete_tuned_model", "kind": "Gdef"}, "embed_content": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.embedding.embed_content", "kind": "Gdef"}, "embed_content_async": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.embedding.embed_content_async", "kind": "Gdef"}, "get_base_model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.get_base_model", "kind": "Gdef"}, "get_file": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.files.get_file", "kind": "Gdef"}, "get_model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.get_model", "kind": "Gdef"}, "get_operation": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.operations.get_operation", "kind": "Gdef"}, "get_tuned_model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.get_tuned_model", "kind": "Gdef"}, "list_files": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.files.list_files", "kind": "Gdef"}, "list_models": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.list_models", "kind": "Gdef"}, "list_operations": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.operations.list_operations", "kind": "Gdef"}, "list_tuned_models": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.list_tuned_models", "kind": "Gdef"}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types", "kind": "Gdef"}, "update_tuned_model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.models.update_tuned_model", "kind": "Gdef"}, "upload_file": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.files.upload_file", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.version", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\__init__.py"}