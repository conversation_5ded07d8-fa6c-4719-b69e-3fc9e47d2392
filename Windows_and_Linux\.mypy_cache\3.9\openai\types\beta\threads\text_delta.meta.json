{"data_mtime": 1754415046, "dep_lines": [6, 5, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.beta.threads.annotation_delta", "openai._models", "typing", "builtins", "_frozen_importlib", "abc", "openai.types.beta.threads.file_citation_delta_annotation", "openai.types.beta.threads.file_path_delta_annotation", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "f7ddf106f01d5d3c29a25584ebbde57ef216b332", "id": "openai.types.beta.threads.text_delta", "ignore_all": true, "interface_hash": "2a81c4b482ea9e81b2c6f798bce55750ef408f00", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta.py", "plugin_data": null, "size": 389, "suppressed": [], "version_id": "1.17.1"}