{".class": "MypyFile", "_fullname": "openai._legacy_response", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIResponseValidationError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.APIResponseValidationError", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncStream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.AsyncStream", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BaseClient": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.BaseClient", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FinalRequestOptions": {".class": "SymbolTableNode", "cross_ref": "openai._models.FinalRequestOptions", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HttpxBinaryResponseContent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._legacy_response.HttpxBinaryResponseContent", "name": "HttpxBinaryResponseContent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._legacy_response.HttpxBinaryResponseContent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._legacy_response", "mro": ["openai._legacy_response.HttpxBinaryResponseContent", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", "httpx._models.Response"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HttpxBinaryResponseContent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aclose of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aiter_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.aiter_bytes", "name": "aiter_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aiter_bytes of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aiter_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.aiter_lines", "name": "aiter_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aiter_lines of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aiter_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.aiter_raw", "name": "aiter_raw", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aiter_raw of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aiter_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.aiter_text", "name": "aiter_text", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aiter_text of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.aread", "name": "aread", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aread of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "astream_to_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "file", "chunk_size"], "dataclass_transform_spec": null, "deprecated": "function openai._legacy_response.HttpxBinaryResponseContent.astream_to_file is deprecated: Due to a bug, this method doesn't actually stream the response content, `.with_streaming_response.method()` should be used instead", "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.astream_to_file", "name": "astream_to_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "file", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "astream_to_file of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.astream_to_file", "name": "astream_to_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "file", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "astream_to_file of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "charset_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.charset_encoding", "name": "charset_encoding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "charset_encoding of HttpxBinaryResponseContent", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.charset_encoding", "name": "charset_encoding", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "charset_encoding of HttpxBinaryResponseContent", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of HttpxBinaryResponseContent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.content", "name": "content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "content of HttpxBinaryResponseContent", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.content", "name": "content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "content of HttpxBinaryResponseContent", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.encoding", "name": "encoding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encoding of HttpxBinaryResponseContent", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.encoding", "name": "encoding", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encoding of HttpxBinaryResponseContent", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iter_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.iter_bytes", "name": "iter_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_bytes of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.iter_lines", "name": "iter_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_lines of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.iter_raw", "name": "iter_raw", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_raw of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.iter_text", "name": "iter_text", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_text of HttpxBinaryResponseContent", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.json", "name": "json", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "json of HttpxBinaryResponseContent", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of HttpxBinaryResponseContent", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.response", "name": "response", "setter_type": null, "type": "httpx._models.Response"}}, "stream_to_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "file", "chunk_size"], "dataclass_transform_spec": null, "deprecated": "function openai._legacy_response.HttpxBinaryResponseContent.stream_to_file is deprecated: Due to a bug, this method doesn't actually stream the response content, `.with_streaming_response.method()` should be used instead", "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.stream_to_file", "name": "stream_to_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "file", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stream_to_file of HttpxBinaryResponseContent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.stream_to_file", "name": "stream_to_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "file", "chunk_size"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stream_to_file of HttpxBinaryResponseContent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of HttpxBinaryResponseContent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.text", "name": "text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of HttpxBinaryResponseContent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "write_to_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.HttpxBinaryResponseContent.write_to_file", "name": "write_to_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file"], "arg_types": ["openai._legacy_response.HttpxBinaryResponseContent", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write_to_file of HttpxBinaryResponseContent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.HttpxBinaryResponseContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._legacy_response.HttpxBinaryResponseContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LegacyAPIResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._legacy_response.LegacyAPIResponse", "name": "LegacyAPIResponse", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._legacy_response.LegacyAPIResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._legacy_response", "mro": ["openai._legacy_response.LegacyAPIResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["self", "raw", "cast_to", "client", "stream", "stream_cls", "options", "retries_taken"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["self", "raw", "cast_to", "client", "stream", "stream_cls", "options", "retries_taken"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "httpx._models.Response", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "openai._base_client.BaseClient"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "openai._models.FinalRequestOptions", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LegacyAPIResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_cast_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse._cast_to", "name": "_cast_to", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse._client", "name": "_client", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "openai._base_client.BaseClient"}}}, "_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse._options", "name": "_options", "setter_type": null, "type": "openai._models.FinalRequestOptions"}}, "_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "to"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse._parse", "name": "_parse", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "to"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse._parse", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse of LegacyAPIResponse", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse._parse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse._parse", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_parsed_by_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse._parsed_by_type", "name": "_parsed_by_type", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse._stream", "name": "_stream", "setter_type": null, "type": "builtins.bool"}}, "_stream_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse._stream_cls", "name": "_stream_cls", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.content", "name": "content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "content of LegacyAPIResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.content", "name": "content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "content of LegacyAPIResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "elapsed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.elapsed", "name": "elapsed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "elapsed of LegacyAPIResponse", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.elapsed", "name": "elapsed", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "elapsed of LegacyAPIResponse", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of LegacyAPIResponse", "ret_type": "httpx._models.Headers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.headers", "name": "headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of LegacyAPIResponse", "ret_type": "httpx._models.Headers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "http_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.http_request", "name": "http_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "http_request of LegacyAPIResponse", "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.http_request", "name": "http_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "http_request of LegacyAPIResponse", "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "http_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse.http_response", "name": "http_response", "setter_type": null, "type": "httpx._models.Response"}}, "http_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.http_version", "name": "http_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "http_version of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.http_version", "name": "http_version", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "http_version of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_closed of LegacyAPIResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.is_closed", "name": "is_closed", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_closed of LegacyAPIResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.method", "name": "method", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "method of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.method", "name": "method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "method of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "openai._legacy_response.LegacyAPIResponse.parse", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "to"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "to"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "to"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "to"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.parse", "name": "parse", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "to"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.parse", "name": "parse", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "to"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "id": -1, "name": "_T", "namespace": "openai._legacy_response.LegacyAPIResponse.parse#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of LegacyAPIResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "request_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.request_id", "name": "request_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request_id of LegacyAPIResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.request_id", "name": "request_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request_id of LegacyAPIResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retries_taken": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._legacy_response.LegacyAPIResponse.retries_taken", "name": "retries_taken", "setter_type": null, "type": "builtins.int"}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.status_code", "name": "status_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "status_code of LegacyAPIResponse", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.status_code", "name": "status_code", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "status_code of LegacyAPIResponse", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.text", "name": "text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text of LegacyAPIResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._legacy_response.LegacyAPIResponse.url", "name": "url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "url of LegacyAPIResponse", "ret_type": "httpx._urls.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._legacy_response.LegacyAPIResponse.url", "name": "url", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "url of LegacyAPIResponse", "ret_type": "httpx._urls.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.LegacyAPIResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": 1, "name": "R", "namespace": "openai._legacy_response.LegacyAPIResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["R"], "typeddict_type": null}}, "MissingStreamClassError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._legacy_response.MissingStreamClassError", "name": "MissingStreamClassError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._legacy_response.MissingStreamClassError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._legacy_response", "mro": ["openai._legacy_response.MissingStreamClassError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._legacy_response.MissingStreamClassError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._legacy_response.MissingStreamClassError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MissingStreamClassError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.MissingStreamClassError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._legacy_response.MissingStreamClassError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoneType": {".class": "SymbolTableNode", "cross_ref": "openai._types.NoneType", "kind": "Gdef"}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.P", "name": "P", "upper_bound": "builtins.object", "variance": 0}}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef"}, "R": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "name": "R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "RAW_RESPONSE_HEADER": {".class": "SymbolTableNode", "cross_ref": "openai._constants.RAW_RESPONSE_HEADER", "kind": "Gdef"}, "Stream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.Stream", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._legacy_response.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._legacy_response.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._legacy_response.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._legacy_response.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._legacy_response.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._legacy_response.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_request_id": {".class": "SymbolTableNode", "cross_ref": "openai._models.add_request_id", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anyio": {".class": "SymbolTableNode", "cross_ref": "anyio", "kind": "Gdef"}, "async_to_raw_response_wrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai._legacy_response.async_to_raw_response_wrapper", "name": "async_to_raw_response_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": -2, "name": "R", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "async_to_raw_response_wrapper", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": -2, "name": "R", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": -2, "name": "R", "namespace": "openai._legacy_response.async_to_raw_response_wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef"}, "extract_stream_chunk_type": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.extract_stream_chunk_type", "kind": "Gdef"}, "extract_type_arg": {".class": "SymbolTableNode", "cross_ref": "openai._utils._typing.extract_type_arg", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_annotated_type": {".class": "SymbolTableNode", "cross_ref": "openai._utils._typing.is_annotated_type", "kind": "Gdef"}, "is_basemodel": {".class": "SymbolTableNode", "cross_ref": "openai._models.is_basemodel", "kind": "Gdef"}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef"}, "is_stream_class_type": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.is_stream_class_type", "kind": "Gdef"}, "is_type_alias_type": {".class": "SymbolTableNode", "cross_ref": "openai._utils._typing.is_type_alias_type", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._legacy_response.log", "name": "log", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "pydantic": {".class": "SymbolTableNode", "cross_ref": "pydantic", "kind": "Gdef"}, "to_raw_response_wrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai._legacy_response.to_raw_response_wrapper", "name": "to_raw_response_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": -2, "name": "R", "namespace": "openai._legacy_response.to_raw_response_wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_raw_response_wrapper", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": -2, "name": "R", "namespace": "openai._legacy_response.to_raw_response_wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "openai._legacy_response.P", "id": -1, "name": "P", "namespace": "openai._legacy_response.to_raw_response_wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._legacy_response.R", "id": -2, "name": "R", "namespace": "openai._legacy_response.to_raw_response_wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\_legacy_response.py"}