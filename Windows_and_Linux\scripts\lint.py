#!/usr/bin/env python3
"""
Linting script for Writing Tools project
Runs ruff, black, isort, and mypy on the codebase
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{'='*50}")
    print(f"Running {description}...")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*50}")

    try:
        result = subprocess.run(
            cmd,
            check=False,
            capture_output=True,
            text=True,
            encoding="utf-8",
            errors="replace",
            cwd=Path(__file__).parent.parent,
        )

        if result.stdout:
            print("STDOUT:")
            print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        print(f"❌ {description} failed with return code {result.returncode}")
        return False

    except FileNotFoundError:
        print(f"❌ {description} failed: Command not found")
        return False
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False


def main():
    """Main linting function"""
    print("🔍 Starting code quality checks for Writing Tools...")

    # Define the commands to run
    commands = [
        (["python", "-m", "ruff", "check", ".", "--fix", "--exclude", "myvenv"], "Ruff linting (with auto-fix)"),
        (["python", "-m", "black", ".", "--check", "--exclude", "myvenv"], "Black formatting check"),
        (["python", "-m", "isort", ".", "--check-only", "--skip", "myvenv"], "Import sorting check"),
        (["python", "-m", "mypy", ".", "--ignore-missing-imports", "--exclude", "myvenv"], "MyPy type checking"),
    ]

    results = []

    for cmd, description in commands:
        success = run_command(cmd, description)
        results.append((description, success))

    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")

    all_passed = True
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{description}: {status}")
        if not success:
            all_passed = False

    if all_passed:
        print("\n🎉 All checks passed!")
        return 0
    print("\n⚠️  Some checks failed. Please fix the issues above.")
    return 1


if __name__ == "__main__":
    sys.exit(main())
