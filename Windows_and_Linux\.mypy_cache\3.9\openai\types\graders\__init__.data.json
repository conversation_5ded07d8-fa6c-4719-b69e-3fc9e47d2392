{".class": "MypyFile", "_fullname": "openai.types.graders", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LabelModelGrader": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.label_model_grader.LabelModelGrader", "kind": "Gdef"}, "LabelModelGraderParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.label_model_grader_param.LabelModelGraderParam", "kind": "Gdef"}, "MultiGrader": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.multi_grader.MultiGrader", "kind": "Gdef"}, "MultiGraderParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.multi_grader_param.MultiGraderParam", "kind": "Gdef"}, "PythonGrader": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.python_grader.PythonGrader", "kind": "Gdef"}, "PythonGraderParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.python_grader_param.PythonGraderParam", "kind": "Gdef"}, "ScoreModelGrader": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.score_model_grader.ScoreModelGrader", "kind": "Gdef"}, "ScoreModelGraderParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.score_model_grader_param.ScoreModelGraderParam", "kind": "Gdef"}, "StringCheckGrader": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.string_check_grader.StringCheckGrader", "kind": "Gdef"}, "StringCheckGraderParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.string_check_grader_param.StringCheckGraderParam", "kind": "Gdef"}, "TextSimilarityGrader": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.text_similarity_grader.TextSimilarityGrader", "kind": "Gdef"}, "TextSimilarityGraderParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.graders.text_similarity_grader_param.TextSimilarityGraderParam", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\graders\\__init__.py"}