{".class": "MypyFile", "_fullname": "openai.lib.streaming.chat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncChatCompletionStream": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "kind": "Gdef"}, "AsyncChatCompletionStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "kind": "Gdef"}, "ChatCompletionStream": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream", "kind": "Gdef"}, "ChatCompletionStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent", "kind": "Gdef"}, "ChatCompletionStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "kind": "Gdef"}, "ChatCompletionStreamState": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "kind": "Gdef"}, "ChunkEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ChunkEvent", "kind": "Gdef"}, "ContentDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ContentDeltaEvent", "kind": "Gdef"}, "ContentDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ContentDoneEvent", "kind": "Gdef"}, "FunctionToolCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.FunctionToolCallArgumentsDeltaEvent", "kind": "Gdef"}, "FunctionToolCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.FunctionToolCallArgumentsDoneEvent", "kind": "Gdef"}, "LogprobsContentDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsContentDeltaEvent", "kind": "Gdef"}, "LogprobsContentDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsContentDoneEvent", "kind": "Gdef"}, "LogprobsRefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsRefusalDeltaEvent", "kind": "Gdef"}, "LogprobsRefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsRefusalDoneEvent", "kind": "Gdef"}, "ParsedChatCompletionMessageSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionMessageSnapshot", "kind": "Gdef"}, "ParsedChatCompletionSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot", "kind": "Gdef"}, "ParsedChoiceSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._types.ParsedChoiceSnapshot", "kind": "Gdef"}, "RefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.RefusalDeltaEvent", "kind": "Gdef"}, "RefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.RefusalDoneEvent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\__init__.py"}