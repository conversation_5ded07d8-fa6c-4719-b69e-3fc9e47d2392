{".class": "MypyFile", "_fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "GenerativeServiceGrpcAsyncIOTransport": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc_asyncio.GenerativeServiceGrpcAsyncIOTransport", "kind": "Gdef"}, "GenerativeServiceGrpcTransport": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc.GenerativeServiceGrpcTransport", "kind": "Gdef"}, "GenerativeServiceRestInterceptor": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.transports.rest.GenerativeServiceRestInterceptor", "kind": "Gdef"}, "GenerativeServiceRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.transports.rest.GenerativeServiceRestTransport", "kind": "Gdef"}, "GenerativeServiceTransport": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.transports.base.GenerativeServiceTransport", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_transport_registry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.generative_service.transports._transport_registry", "name": "_transport_registry", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "google.ai.generativelanguage_v1beta.services.generative_service.transports.base.GenerativeServiceTransport"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\__init__.py"}