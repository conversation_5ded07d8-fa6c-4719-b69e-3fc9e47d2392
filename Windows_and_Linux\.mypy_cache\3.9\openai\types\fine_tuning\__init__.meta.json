{"data_mtime": 1754415047, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.fine_tuning.dpo_method", "openai.types.fine_tuning.fine_tuning_job", "openai.types.fine_tuning.job_list_params", "openai.types.fine_tuning.dpo_method_param", "openai.types.fine_tuning.job_create_params", "openai.types.fine_tuning.supervised_method", "openai.types.fine_tuning.dpo_hyperparameters", "openai.types.fine_tuning.reinforcement_method", "openai.types.fine_tuning.fine_tuning_job_event", "openai.types.fine_tuning.job_list_events_params", "openai.types.fine_tuning.supervised_method_param", "openai.types.fine_tuning.dpo_hyperparameters_param", "openai.types.fine_tuning.reinforcement_method_param", "openai.types.fine_tuning.supervised_hyperparameters", "openai.types.fine_tuning.fine_tuning_job_integration", "openai.types.fine_tuning.reinforcement_hyperparameters", "openai.types.fine_tuning.supervised_hyperparameters_param", "openai.types.fine_tuning.fine_tuning_job_wandb_integration", "openai.types.fine_tuning.reinforcement_hyperparameters_param", "openai.types.fine_tuning.fine_tuning_job_wandb_integration_object", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "710f717bf95e4bf2fb93d59996cab80f4db29f70", "id": "openai.types.fine_tuning", "ignore_all": true, "interface_hash": "70ce9cf394dd59aeb9c6d23fb60fbe27f600f6f9", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\fine_tuning\\__init__.py", "plugin_data": null, "size": 1832, "suppressed": [], "version_id": "1.17.1"}