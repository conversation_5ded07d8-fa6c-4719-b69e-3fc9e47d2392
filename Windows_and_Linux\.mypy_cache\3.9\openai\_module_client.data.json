{".class": "MypyFile", "_fullname": "openai._module_client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Audio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.Audio", "kind": "Gdef"}, "AudioProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.audio.audio.Audio"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.AudioProxy", "name": "AudioProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.AudioProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.AudioProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.AudioProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.AudioProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of AudioProxy", "ret_type": "openai.resources.audio.audio.Audio", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.AudioProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.AudioProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of AudioProxy", "ret_type": "openai.resources.audio.audio.Audio", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.AudioProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.AudioProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Batches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.Batches", "kind": "Gdef"}, "BatchesProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.batches.Batches"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.BatchesProxy", "name": "BatchesProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.BatchesProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.BatchesProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.BatchesProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.BatchesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of BatchesProxy", "ret_type": "openai.resources.batches.Batches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.BatchesProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.BatchesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of BatchesProxy", "ret_type": "openai.resources.batches.Batches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.BatchesProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.BatchesProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Beta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.Beta", "kind": "Gdef"}, "BetaProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.beta.beta.Beta"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.BetaProxy", "name": "BetaProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.BetaProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.BetaProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.BetaProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.BetaProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of BetaProxy", "ret_type": "openai.resources.beta.beta.Beta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.BetaProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.BetaProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of BetaProxy", "ret_type": "openai.resources.beta.beta.Beta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.BetaProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.BetaProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Chat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.Chat", "kind": "Gdef"}, "ChatProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.chat.chat.Chat"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.ChatProxy", "name": "ChatProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.ChatProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.ChatProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.ChatProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ChatProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ChatProxy", "ret_type": "openai.resources.chat.chat.Chat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.ChatProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ChatProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ChatProxy", "ret_type": "openai.resources.chat.chat.Chat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.ChatProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.ChatProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Completions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.Completions", "kind": "Gdef"}, "CompletionsProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.completions.Completions"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.CompletionsProxy", "name": "CompletionsProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.CompletionsProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.CompletionsProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.CompletionsProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.CompletionsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of CompletionsProxy", "ret_type": "openai.resources.completions.Completions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.CompletionsProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.CompletionsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of CompletionsProxy", "ret_type": "openai.resources.completions.Completions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.CompletionsProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.CompletionsProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Containers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.Containers", "kind": "Gdef"}, "ContainersProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.containers.containers.Containers"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.ContainersProxy", "name": "ContainersProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.ContainersProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.ContainersProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.ContainersProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ContainersProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ContainersProxy", "ret_type": "openai.resources.containers.containers.Containers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.ContainersProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ContainersProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ContainersProxy", "ret_type": "openai.resources.containers.containers.Containers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.ContainersProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.ContainersProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Embeddings": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.Embeddings", "kind": "Gdef"}, "EmbeddingsProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.embeddings.Embeddings"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.EmbeddingsProxy", "name": "EmbeddingsProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.EmbeddingsProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.EmbeddingsProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.EmbeddingsProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.EmbeddingsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of EmbeddingsProxy", "ret_type": "openai.resources.embeddings.Embeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.EmbeddingsProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.EmbeddingsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of EmbeddingsProxy", "ret_type": "openai.resources.embeddings.Embeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.EmbeddingsProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.EmbeddingsProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Evals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.Evals", "kind": "Gdef"}, "EvalsProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.evals.evals.Evals"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.EvalsProxy", "name": "EvalsProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.EvalsProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.EvalsProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.EvalsProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.EvalsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of EvalsProxy", "ret_type": "openai.resources.evals.evals.Evals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.EvalsProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.EvalsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of EvalsProxy", "ret_type": "openai.resources.evals.evals.Evals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.EvalsProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.EvalsProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.Files", "kind": "Gdef"}, "FilesProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.files.Files"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.FilesProxy", "name": "FilesProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.FilesProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.FilesProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.FilesProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.FilesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of FilesProxy", "ret_type": "openai.resources.files.Files", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.FilesProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.FilesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of FilesProxy", "ret_type": "openai.resources.files.Files", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.FilesProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.FilesProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuning", "kind": "Gdef"}, "FineTuningProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.fine_tuning.fine_tuning.FineTuning"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.FineTuningProxy", "name": "FineTuningProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.FineTuningProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.FineTuningProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.FineTuningProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.FineTuningProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of FineTuningProxy", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.FineTuningProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.FineTuningProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of FineTuningProxy", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.FineTuningProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.FineTuningProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Images": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.Images", "kind": "Gdef"}, "ImagesProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.images.Images"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.ImagesProxy", "name": "ImagesProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.ImagesProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.ImagesProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.ImagesProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ImagesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ImagesProxy", "ret_type": "openai.resources.images.Images", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.ImagesProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ImagesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ImagesProxy", "ret_type": "openai.resources.images.Images", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.ImagesProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.ImagesProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LazyProxy": {".class": "SymbolTableNode", "cross_ref": "openai._utils._proxy.LazyProxy", "kind": "Gdef"}, "Models": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.Models", "kind": "Gdef"}, "ModelsProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.models.Models"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.ModelsProxy", "name": "ModelsProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.ModelsProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.ModelsProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.ModelsProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ModelsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ModelsProxy", "ret_type": "openai.resources.models.Models", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.ModelsProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ModelsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ModelsProxy", "ret_type": "openai.resources.models.Models", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.ModelsProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.ModelsProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Moderations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.Moderations", "kind": "Gdef"}, "ModerationsProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.moderations.Moderations"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.ModerationsProxy", "name": "ModerationsProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.ModerationsProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.ModerationsProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.ModerationsProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ModerationsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ModerationsProxy", "ret_type": "openai.resources.moderations.Moderations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.ModerationsProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ModerationsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ModerationsProxy", "ret_type": "openai.resources.moderations.Moderations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.ModerationsProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.ModerationsProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Responses": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.Responses", "kind": "Gdef"}, "ResponsesProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.responses.responses.Responses"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.ResponsesProxy", "name": "ResponsesProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.ResponsesProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.ResponsesProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.ResponsesProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ResponsesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ResponsesProxy", "ret_type": "openai.resources.responses.responses.Responses", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.ResponsesProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.ResponsesProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of ResponsesProxy", "ret_type": "openai.resources.responses.responses.Responses", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.ResponsesProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.ResponsesProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Uploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.Uploads", "kind": "Gdef"}, "UploadsProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.uploads.uploads.Uploads"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.UploadsProxy", "name": "UploadsProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.UploadsProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.UploadsProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.UploadsProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.UploadsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of UploadsProxy", "ret_type": "openai.resources.uploads.uploads.Uploads", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.UploadsProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.UploadsProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of UploadsProxy", "ret_type": "openai.resources.uploads.uploads.Uploads", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.UploadsProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.UploadsProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStores", "kind": "Gdef"}, "VectorStoresProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.vector_stores.vector_stores.VectorStores"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.VectorStoresProxy", "name": "VectorStoresProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.VectorStoresProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.VectorStoresProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.VectorStoresProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.VectorStoresProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of VectorStoresProxy", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStores", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.VectorStoresProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.VectorStoresProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of VectorStoresProxy", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStores", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.VectorStoresProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.VectorStoresProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Webhooks": {".class": "SymbolTableNode", "cross_ref": "openai.resources.webhooks.Webhooks", "kind": "Gdef"}, "WebhooksProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["openai.resources.webhooks.Webhooks"], "extra_attrs": null, "type_ref": "openai._utils._proxy.LazyProxy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._module_client.WebhooksProxy", "name": "WebhooksProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._module_client.WebhooksProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai._module_client", "mro": ["openai._module_client.WebhooksProxy", "openai._utils._proxy.LazyProxy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__load__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._module_client.WebhooksProxy.__load__", "name": "__load__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.WebhooksProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of WebhooksProxy", "ret_type": "openai.resources.webhooks.Webhooks", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._module_client.WebhooksProxy.__load__", "name": "__load__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._module_client.WebhooksProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__load__ of WebhooksProxy", "ret_type": "openai.resources.webhooks.Webhooks", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._module_client.WebhooksProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._module_client.WebhooksProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._module_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._module_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._module_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._module_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._module_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._module_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_load_client": {".class": "SymbolTableNode", "cross_ref": "openai._load_client", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "audio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.audio", "name": "audio", "setter_type": null, "type": "openai.resources.audio.audio.Audio"}}, "batches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.batches", "name": "batches", "setter_type": null, "type": "openai.resources.batches.Batches"}}, "beta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.beta", "name": "beta", "setter_type": null, "type": "openai.resources.beta.beta.Beta"}}, "chat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.chat", "name": "chat", "setter_type": null, "type": "openai.resources.chat.chat.Chat"}}, "completions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.completions", "name": "completions", "setter_type": null, "type": "openai.resources.completions.Completions"}}, "containers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.containers", "name": "containers", "setter_type": null, "type": "openai.resources.containers.containers.Containers"}}, "embeddings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.embeddings", "name": "embeddings", "setter_type": null, "type": "openai.resources.embeddings.Embeddings"}}, "evals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.evals", "name": "evals", "setter_type": null, "type": "openai.resources.evals.evals.Evals"}}, "files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.files", "name": "files", "setter_type": null, "type": "openai.resources.files.Files"}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": "openai.resources.fine_tuning.fine_tuning.FineTuning"}}, "images": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.images", "name": "images", "setter_type": null, "type": "openai.resources.images.Images"}}, "models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.models", "name": "models", "setter_type": null, "type": "openai.resources.models.Models"}}, "moderations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.moderations", "name": "moderations", "setter_type": null, "type": "openai.resources.moderations.Moderations"}}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "responses": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.responses", "name": "responses", "setter_type": null, "type": "openai.resources.responses.responses.Responses"}}, "uploads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.uploads", "name": "uploads", "setter_type": null, "type": "openai.resources.uploads.uploads.Uploads"}}, "vector_stores": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.vector_stores", "name": "vector_stores", "setter_type": null, "type": "openai.resources.vector_stores.vector_stores.VectorStores"}}, "webhooks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai._module_client.webhooks", "name": "webhooks", "setter_type": null, "type": "openai.resources.webhooks.Webhooks"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\_module_client.py"}