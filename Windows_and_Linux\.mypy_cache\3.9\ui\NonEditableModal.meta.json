{"data_mtime": 1754415020, "dep_lines": [5, 5, 5, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4], "dep_prios": [5, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["PySide6.QtCore", "PySide6.QtGui", "PySide6.QtWidgets", "logging", "PySide6", "builtins", "_frozen_importlib", "abc", "enum", "shiboken6", "shiboken6.Shiboken", "types", "typing"], "hash": "57c4874a7a7141f500f1d80d2a575bd860c7543c", "id": "ui.NonEditableModal", "ignore_all": false, "interface_hash": "da8dbfe8f9713abf881fcc837e94d37fa815abfd", "mtime": 1754415154, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": ".\\ui\\NonEditableModal.py", "plugin_data": null, "size": 5476, "suppressed": ["markdown2", "pyperclip"], "version_id": "1.17.1"}