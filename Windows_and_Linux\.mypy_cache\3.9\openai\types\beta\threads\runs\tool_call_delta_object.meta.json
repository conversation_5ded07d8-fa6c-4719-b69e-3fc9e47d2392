{"data_mtime": 1754415046, "dep_lines": [7, 6, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.beta.threads.runs.tool_call_delta", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "openai.types.beta.threads.runs.code_interpreter_tool_call_delta", "openai.types.beta.threads.runs.file_search_tool_call_delta", "openai.types.beta.threads.runs.function_tool_call_delta", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "db6ce0b34e7e2c88583f9fedde50617011e967f6", "id": "openai.types.beta.threads.runs.tool_call_delta_object", "ignore_all": true, "interface_hash": "5d442a2c5b49f470a6304e463f8d77421755ed65", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta_object.py", "plugin_data": null, "size": 615, "suppressed": [], "version_id": "1.17.1"}