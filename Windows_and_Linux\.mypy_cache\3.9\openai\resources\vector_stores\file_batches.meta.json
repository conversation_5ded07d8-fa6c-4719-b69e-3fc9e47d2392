{"data_mtime": 1754415047, "dep_lines": [23, 23, 25, 26, 22, 23, 24, 8, 13, 14, 15, 16, 17, 18, 19, 20, 21, 3, 5, 6, 7, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 699], "dep_prios": [10, 10, 5, 5, 5, 20, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["openai.types.vector_stores.file_batch_create_params", "openai.types.vector_stores.file_batch_list_files_params", "openai.types.vector_stores.vector_store_file", "openai.types.vector_stores.vector_store_file_batch", "openai.types.file_object", "openai.types.vector_stores", "openai.types.file_chunking_strategy_param", "concurrent.futures", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "asyncio", "typing", "typing_extensions", "httpx", "sniffio", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types.auto_file_chunking_strategy_param", "openai.types.static_file_chunking_strategy_object_param", "openai.types.static_file_chunking_strategy_param", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "9b8a3de2f9de38ea27746664bfe4d44ab2dfd370", "id": "openai.resources.vector_stores.file_batches", "ignore_all": true, "interface_hash": "336e5ad657c66252db4bf2263e7c011a3009ed4b", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\vector_stores\\file_batches.py", "plugin_data": null, "size": 33076, "suppressed": ["trio"], "version_id": "1.17.1"}