{".class": "MypyFile", "_fullname": "openai.types.image_edit_partial_image_event", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef", "module_public": false}, "ImageEditPartialImageEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent", "name": "ImageEditPartialImageEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 173, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 176, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 179, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 11, "name": "b64_json", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 14, "name": "background", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 17, "name": "created_at", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 20, "name": "output_format", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 23, "name": "partial_image_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 26, "name": "quality", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 29, "name": "size", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 32, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "image_edit.partial_image"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.image_edit_partial_image_event", "mro": ["openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "b64_json", "background", "created_at", "output_format", "partial_image_index", "quality", "size", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "b64_json", "background", "created_at", "output_format", "partial_image_index", "quality", "size", "type"], "arg_types": ["openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image_edit.partial_image"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ImageEditPartialImageEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "b64_json", "background", "created_at", "output_format", "partial_image_index", "quality", "size", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "b64_json", "background", "created_at", "output_format", "partial_image_index", "quality", "size", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image_edit.partial_image"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ImageEditPartialImageEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "b64_json", "background", "created_at", "output_format", "partial_image_index", "quality", "size", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image_edit.partial_image"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ImageEditPartialImageEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "b64_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.b64_json", "name": "b64_json", "setter_type": null, "type": "builtins.str"}}, "background": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.background", "name": "background", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.created_at", "name": "created_at", "setter_type": null, "type": "builtins.int"}}, "output_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.output_format", "name": "output_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}], "uses_pep604_syntax": false}}}, "partial_image_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.partial_image_index", "name": "partial_image_index", "setter_type": null, "type": "builtins.int"}}, "quality": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.quality", "name": "quality", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.size", "name": "size", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": false}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.type", "name": "type", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "image_edit.partial_image"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.image_edit_partial_image_event.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.image_edit_partial_image_event.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.image_edit_partial_image_event.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.image_edit_partial_image_event.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.image_edit_partial_image_event.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.image_edit_partial_image_event.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.image_edit_partial_image_event.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\image_edit_partial_image_event.py"}