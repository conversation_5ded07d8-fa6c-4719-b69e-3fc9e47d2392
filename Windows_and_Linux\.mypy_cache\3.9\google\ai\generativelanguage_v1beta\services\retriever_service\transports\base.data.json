{".class": "MypyFile", "_fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.DEFAULT_CLIENT_INFO", "name": "DEFAULT_CLIENT_INFO", "setter_type": null, "type": "google.api_core.gapic_v1.client_info.ClientInfo"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "RetrieverServiceTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport", "name": "RetrieverServiceTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base", "mro": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "AUTH_SCOPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.AUTH_SCOPES", "name": "AUTH_SCOPES", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "DEFAULT_HOST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.DEFAULT_HOST", "name": "DEFAULT_HOST", "setter_type": null, "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "host", "credentials", "credentials_file", "scopes", "quota_project_id", "client_info", "always_use_jwt_access", "api_audience", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "host", "credentials", "credentials_file", "scopes", "quota_project_id", "client_info", "always_use_jwt_access", "api_audience", "kwargs"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RetrieverServiceTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport._credentials", "name": "_credentials", "setter_type": null, "type": {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport._host", "name": "_host", "setter_type": null, "type": "builtins.str"}}, "_ignore_credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport._ignore_credentials", "name": "_ignore_credentials", "setter_type": null, "type": "builtins.bool"}}, "_prep_wrapped_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport._prep_wrapped_messages", "name": "_prep_wrapped_messages", "type": null}}, "_scopes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport._scopes", "name": "_scopes", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_wrapped_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport._wrapped_methods", "name": "_wrapped_methods", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "batch_create_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.batch_create_chunks", "name": "batch_create_chunks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_create_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.batch_create_chunks", "name": "batch_create_chunks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_create_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batch_delete_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.batch_delete_chunks", "name": "batch_delete_chunks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_delete_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchDeleteChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.batch_delete_chunks", "name": "batch_delete_chunks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_delete_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchDeleteChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batch_update_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.batch_update_chunks", "name": "batch_update_chunks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_update_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.batch_update_chunks", "name": "batch_update_chunks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_update_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.close", "name": "close", "type": null}}, "create_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.create_chunk", "name": "create_chunk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.CreateChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.create_chunk", "name": "create_chunk", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.CreateChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.create_corpus", "name": "create_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.CreateCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.create_corpus", "name": "create_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.CreateCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.create_document", "name": "create_document", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.CreateDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Document", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Document"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.create_document", "name": "create_document", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.CreateDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Document", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Document"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.delete_chunk", "name": "delete_chunk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.DeleteChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.delete_chunk", "name": "delete_chunk", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.DeleteChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.delete_corpus", "name": "delete_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.DeleteCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.delete_corpus", "name": "delete_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.DeleteCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.delete_document", "name": "delete_document", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.DeleteDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.delete_document", "name": "delete_document", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.DeleteDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_chunk", "name": "get_chunk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.GetChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_chunk", "name": "get_chunk", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.GetChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_corpus", "name": "get_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.GetCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_corpus", "name": "get_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.GetCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_document", "name": "get_document", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.GetDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Document", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Document"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_document", "name": "get_document", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.GetDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Document", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Document"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_operation", "name": "get_operation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.host", "name": "host", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.host", "name": "host", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "host of RetrieverServiceTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of RetrieverServiceTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of RetrieverServiceTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_chunks", "name": "list_chunks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_chunks", "name": "list_chunks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_chunks of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_corpora": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_corpora", "name": "list_corpora", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_corpora of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_corpora", "name": "list_corpora", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_corpora of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_documents", "name": "list_documents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_documents of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_documents", "name": "list_documents", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_documents of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_operations", "name": "list_operations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "query_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.query_corpus", "name": "query_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "query_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.query_corpus", "name": "query_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "query_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "query_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.query_document", "name": "query_document", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "query_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.query_document", "name": "query_document", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "query_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentResponse", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.update_chunk", "name": "update_chunk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.UpdateChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.update_chunk", "name": "update_chunk", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_chunk of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.UpdateChunkRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Chunk"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.update_corpus", "name": "update_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.UpdateCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.update_corpus", "name": "update_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_corpus of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.UpdateCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Corpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.update_document", "name": "update_document", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.UpdateDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Document", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Document"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.update_document", "name": "update_document", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_document of RetrieverServiceTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.retriever_service.UpdateDocumentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.retriever.Document", {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.retriever.Document"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.RetrieverServiceTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_public": false}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef", "module_public": false}, "empty_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "name": "empty_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.empty_pb2", "source_any": null, "type_of_any": 3}}}, "ga_credentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.credentials", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef", "module_public": false}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.retriever_service.transports.base.operations_pb2", "source_any": null, "type_of_any": 3}}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.gapic_version", "kind": "Gdef", "module_public": false}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry", "kind": "Gdef", "module_public": false}, "retriever": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever", "kind": "Gdef", "module_public": false}, "retriever_service": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service", "kind": "Gdef", "module_public": false}, "service_account": {".class": "SymbolTableNode", "cross_ref": "google.oauth2.service_account", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\base.py"}