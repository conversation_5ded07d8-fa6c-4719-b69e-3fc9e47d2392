{"data_mtime": 1754414817, "dep_lines": [8, 14, 15, 6, 7, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging.handlers", "config.interfaces", "config.data_operations", "json", "logging", "os", "sys", "pathlib", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "json.encoder", "types", "typing_extensions"], "hash": "07dec419c5e1c19eb325da1e0c3d8fe49c314ad9", "id": "config.settings", "ignore_all": false, "interface_hash": "afb559e0ffa21063a7909e8c4077d1c98ce5447d", "mtime": 1754414816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\config\\settings.py", "plugin_data": null, "size": 10545, "suppressed": [], "version_id": "1.17.1"}