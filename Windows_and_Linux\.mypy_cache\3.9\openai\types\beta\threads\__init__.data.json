{".class": "MypyFile", "_fullname": "openai.types.beta.threads", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotation": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.annotation.Annotation", "kind": "Gdef"}, "AnnotationDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.annotation_delta.AnnotationDelta", "kind": "Gdef"}, "FileCitationAnnotation": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.file_citation_annotation.FileCitationAnnotation", "kind": "Gdef"}, "FileCitationDeltaAnnotation": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.file_citation_delta_annotation.FileCitationDeltaAnnotation", "kind": "Gdef"}, "FilePathAnnotation": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.file_path_annotation.FilePathAnnotation", "kind": "Gdef"}, "FilePathDeltaAnnotation": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.file_path_delta_annotation.FilePathDeltaAnnotation", "kind": "Gdef"}, "ImageFile": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file.ImageFile", "kind": "Gdef"}, "ImageFileContentBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file_content_block.ImageFileContentBlock", "kind": "Gdef"}, "ImageFileContentBlockParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam", "kind": "Gdef"}, "ImageFileDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file_delta.ImageFileDelta", "kind": "Gdef"}, "ImageFileDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file_delta_block.ImageFileDeltaBlock", "kind": "Gdef"}, "ImageFileParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file_param.ImageFileParam", "kind": "Gdef"}, "ImageURL": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url.ImageURL", "kind": "Gdef"}, "ImageURLContentBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url_content_block.ImageURLContentBlock", "kind": "Gdef"}, "ImageURLContentBlockParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam", "kind": "Gdef"}, "ImageURLDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url_delta.ImageURLDelta", "kind": "Gdef"}, "ImageURLDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url_delta_block.ImageURLDeltaBlock", "kind": "Gdef"}, "ImageURLParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url_param.ImageURLParam", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message.Message", "kind": "Gdef"}, "MessageContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_content.MessageContent", "kind": "Gdef"}, "MessageContentDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_content_delta.MessageContentDelta", "kind": "Gdef"}, "MessageContentPartParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_content_part_param.MessageContentPartParam", "kind": "Gdef"}, "MessageCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_create_params.MessageCreateParams", "kind": "Gdef"}, "MessageDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_deleted.MessageDeleted", "kind": "Gdef"}, "MessageDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_delta.MessageDelta", "kind": "Gdef"}, "MessageDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_delta_event.MessageDeltaEvent", "kind": "Gdef"}, "MessageListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_list_params.MessageListParams", "kind": "Gdef"}, "MessageUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_update_params.MessageUpdateParams", "kind": "Gdef"}, "RefusalContentBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.refusal_content_block.RefusalContentBlock", "kind": "Gdef"}, "RefusalDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.refusal_delta_block.RefusalDeltaBlock", "kind": "Gdef"}, "RequiredActionFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.required_action_function_tool_call.RequiredActionFunctionToolCall", "kind": "Gdef"}, "Run": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run.Run", "kind": "Gdef"}, "RunCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run_create_params.RunCreateParams", "kind": "Gdef"}, "RunListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run_list_params.RunListParams", "kind": "Gdef"}, "RunStatus": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run_status.RunStatus", "kind": "Gdef"}, "RunSubmitToolOutputsParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run_submit_tool_outputs_params.RunSubmitToolOutputsParams", "kind": "Gdef"}, "RunUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run_update_params.RunUpdateParams", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text.Text", "kind": "Gdef"}, "TextContentBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text_content_block.TextContentBlock", "kind": "Gdef"}, "TextContentBlockParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text_content_block_param.TextContentBlockParam", "kind": "Gdef"}, "TextDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text_delta.TextDelta", "kind": "Gdef"}, "TextDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text_delta_block.TextDeltaBlock", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\threads\\__init__.py"}