{".class": "MypyFile", "_fullname": "openai.types.beta.threads.runs", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CodeInterpreterLogs": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.code_interpreter_logs.CodeInterpreterLogs", "kind": "Gdef"}, "CodeInterpreterOutputImage": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.code_interpreter_output_image.CodeInterpreterOutputImage", "kind": "Gdef"}, "CodeInterpreterToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.code_interpreter_tool_call.CodeInterpreterToolCall", "kind": "Gdef"}, "CodeInterpreterToolCallDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.code_interpreter_tool_call_delta.CodeInterpreterToolCallDelta", "kind": "Gdef"}, "FileSearchToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.file_search_tool_call.FileSearchToolCall", "kind": "Gdef"}, "FileSearchToolCallDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.file_search_tool_call_delta.FileSearchToolCallDelta", "kind": "Gdef"}, "FunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.function_tool_call.FunctionToolCall", "kind": "Gdef"}, "FunctionToolCallDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.function_tool_call_delta.FunctionToolCallDelta", "kind": "Gdef"}, "MessageCreationStepDetails": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.message_creation_step_details.MessageCreationStepDetails", "kind": "Gdef"}, "RunStep": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step.RunStep", "kind": "Gdef"}, "RunStepDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step_delta.RunStepDelta", "kind": "Gdef"}, "RunStepDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step_delta_event.RunStepDeltaEvent", "kind": "Gdef"}, "RunStepDeltaMessageDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step_delta_message_delta.RunStepDeltaMessageDelta", "kind": "Gdef"}, "RunStepInclude": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step_include.RunStepInclude", "kind": "Gdef"}, "StepListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.step_list_params.StepListParams", "kind": "Gdef"}, "StepRetrieveParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.step_retrieve_params.StepRetrieveParams", "kind": "Gdef"}, "ToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.tool_call.ToolCall", "kind": "Gdef"}, "ToolCallDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.tool_call_delta.ToolCallDelta", "kind": "Gdef"}, "ToolCallDeltaObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.tool_call_delta_object.ToolCallDeltaObject", "kind": "Gdef"}, "ToolCallsStepDetails": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.tool_calls_step_details.ToolCallsStepDetails", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.runs.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\__init__.py"}