{"data_mtime": 1754415052, "dep_lines": [14, 15, 17, 18, 20, 21, 22, 28, 10, 14, 15, 17, 20, 1, 3, 4, 5, 6, 7, 8, 9, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 25, 11, 24], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 20, 10, 20, 5, 10, 10, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 20], "dependencies": ["google.ai.generativelanguage", "google.generativeai.protos", "google.auth.credentials", "google.auth.exceptions", "google.api_core.client_options", "google.api_core.gapic_v1", "google.api_core.operations_v1", "google.generativeai.version", "collections.abc", "google.ai", "google.generativeai", "google.auth", "google.api_core", "__future__", "os", "contextlib", "inspect", "dataclasses", "pathlib", "threading", "typing", "io", "google", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_thread", "_typeshed", "abc", "enum", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.cache_service", "google.ai.generativelanguage_v1beta.services.cache_service.client", "google.ai.generativelanguage_v1beta.services.file_service", "google.ai.generativelanguage_v1beta.services.file_service.async_client", "google.ai.generativelanguage_v1beta.services.file_service.client", "google.ai.generativelanguage_v1beta.services.file_service.transports", "google.ai.generativelanguage_v1beta.services.file_service.transports.base", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.generative_service.async_client", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.services.model_service", "google.ai.generativelanguage_v1beta.services.model_service.async_client", "google.ai.generativelanguage_v1beta.services.permission_service", "google.ai.generativelanguage_v1beta.services.permission_service.async_client", "google.ai.generativelanguage_v1beta.services.permission_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.file", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.operations_v1.operations_client", "google.auth._credentials_base", "types"], "hash": "e23a82b16a8c144f36e95570ae8b0a193be2b897", "id": "google.generativeai.client", "ignore_all": true, "interface_hash": "5143ba78df91bb419cfb82e4c8aec41bee3fc2f7", "mtime": 1754404370, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\client.py", "plugin_data": null, "size": 15071, "suppressed": ["googleapiclient.http", "googleapiclient.discovery", "httplib2", "googleapiclient"], "version_id": "1.17.1"}