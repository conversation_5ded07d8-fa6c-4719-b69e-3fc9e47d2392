{"data_mtime": 1754415051, "dep_lines": [60, 61, 51, 59, 51, 52, 55, 57, 39, 52, 32, 33, 34, 35, 36, 37, 39, 64, 32, 36, 37, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 46, 47], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 10, 10, 20, 10, 10, 10, 5, 10, 10, 20, 10, 20, 20, 20, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["google.ai.generativelanguage_v1beta.services.cache_service.transports.base", "google.ai.generativelanguage_v1beta.services.cache_service.transports.grpc_asyncio", "google.ai.generativelanguage_v1beta.services.cache_service.pagers", "google.ai.generativelanguage_v1beta.services.cache_service.client", "google.ai.generativelanguage_v1beta.services.cache_service", "google.ai.generativelanguage_v1beta.types.cached_content", "google.ai.generativelanguage_v1beta.types.cache_service", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.gapic_version", "google.ai.generativelanguage_v1beta.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry_async", "google.api_core.client_options", "google.auth.credentials", "google.oauth2.service_account", "google.ai.generativelanguage_v1beta", "google.api_core.client_logging", "google.api_core", "google.auth", "google.oauth2", "collections", "logging", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.ai.generativelanguage_v1beta.services.cache_service.transports", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "types"], "hash": "f899f5c71f61f148c10b912d39980e1cbabc439c", "id": "google.ai.generativelanguage_v1beta.services.cache_service.async_client", "ignore_all": true, "interface_hash": "f253920cb9f48f0659a0d03aaa72fbed54115335", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\async_client.py", "plugin_data": null, "size": 40561, "suppressed": ["google.longrunning", "google.protobuf"], "version_id": "1.17.1"}