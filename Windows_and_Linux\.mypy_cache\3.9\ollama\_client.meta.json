{"data_mtime": 1754415051, "dep_lines": [6, 26, 28, 33, 35, 44, 1, 2, 3, 4, 5, 6, 7, 9, 10, 25, 35, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 5, 10, 10, 5, 10, 10, 20, 5, 5, 5, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib.parse", "pydantic.json_schema", "ollama._utils", "collections.abc", "importlib.metadata", "ollama._types", "ipaddress", "json", "os", "platform", "sys", "urllib", "<PERSON><PERSON><PERSON>", "pathlib", "typing", "anyio", "importlib", "httpx", "builtins", "_collections_abc", "_frozen_importlib", "_hashlib", "_typeshed", "abc", "anyio._core", "anyio._core._fileio", "anyio.abc", "anyio.abc._resources", "httpx._exceptions", "httpx._models", "json.decoder", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "typing_extensions"], "hash": "a7842128ba01169870d9a759750f8e625053ab1c", "id": "ollama._client", "ignore_all": true, "interface_hash": "2608728aca515dbaec5b09c04aad08b320c9ab7b", "mtime": 1752463753, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\ollama\\_client.py", "plugin_data": null, "size": 34720, "suppressed": [], "version_id": "1.17.1"}