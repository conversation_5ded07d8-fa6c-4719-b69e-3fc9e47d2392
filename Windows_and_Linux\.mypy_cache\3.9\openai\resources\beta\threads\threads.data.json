{".class": "MypyFile", "_fullname": "openai.resources.beta.threads.threads", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AssistantEventHandler": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "AssistantEventHandlerT": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AssistantEventHandlerT", "kind": "Gdef", "module_public": false}, "AssistantResponseFormatOptionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam", "kind": "Gdef", "module_public": false}, "AssistantStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent", "kind": "Gdef", "module_public": false}, "AssistantStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AssistantStreamManager", "kind": "Gdef", "module_public": false}, "AssistantToolChoiceOptionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam", "kind": "Gdef", "module_public": false}, "AssistantToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam", "kind": "Gdef", "module_public": false}, "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncAssistantEventHandler": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "kind": "Gdef", "module_public": false}, "AsyncAssistantEventHandlerT": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "kind": "Gdef", "module_public": false}, "AsyncAssistantStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "kind": "Gdef", "module_public": false}, "AsyncMessages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.AsyncMessages", "kind": "Gdef", "module_public": false}, "AsyncMessagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.AsyncMessagesWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncMessagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.AsyncMessagesWithStreamingResponse", "kind": "Gdef", "module_public": false}, "AsyncRuns": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRuns", "kind": "Gdef", "module_public": false}, "AsyncRunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRunsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncRunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRunsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "AsyncStream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.AsyncStream", "kind": "Gdef", "module_public": false}, "AsyncThreads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.threads.threads.AsyncThreads", "name": "AsyncThreads", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.AsyncThreads", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.threads.threads", "mro": ["openai.resources.beta.threads.threads.AsyncThreads", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.AsyncThreads.create is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_and_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": "function openai.resources.beta.threads.threads.AsyncThreads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": [], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.AsyncThreads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "overload def (self: openai.resources.beta.threads.threads.AsyncThreads, *, assistant_id: builtins.str, instructions: builtins.str | None | openai._types.NotGiven =, max_completion_tokens: builtins.int | None | openai._types.NotGiven =, max_prompt_tokens: builtins.int | None | openai._types.NotGiven =, metadata: builtins.dict[builtins.str, builtins.str] | None | openai._types.NotGiven =, model: builtins.str | Literal['gpt-4.1'] | Literal['gpt-4.1-mini'] | Literal['gpt-4.1-nano'] | Literal['gpt-4.1-2025-04-14'] | Literal['gpt-4.1-mini-2025-04-14'] | Literal['gpt-4.1-nano-2025-04-14'] | Literal['o4-mini'] | Literal['o4-mini-2025-04-16'] | Literal['o3'] | Literal['o3-2025-04-16'] | Literal['o3-mini'] | Literal['o3-mini-2025-01-31'] | Literal['o1'] | Literal['o1-2024-12-17'] | Literal['o1-preview'] | Literal['o1-preview-2024-09-12'] | Literal['o1-mini'] | Literal['o1-mini-2024-09-12'] | Literal['gpt-4o'] | Literal['gpt-4o-2024-11-20'] | Literal['gpt-4o-2024-08-06'] | Literal['gpt-4o-2024-05-13'] | Literal['gpt-4o-audio-preview'] | Literal['gpt-4o-audio-preview-2024-10-01'] | Literal['gpt-4o-audio-preview-2024-12-17'] | Literal['gpt-4o-audio-preview-2025-06-03'] | Literal['gpt-4o-mini-audio-preview'] | Literal['gpt-4o-mini-audio-preview-2024-12-17'] | Literal['gpt-4o-search-preview'] | Literal['gpt-4o-mini-search-preview'] | Literal['gpt-4o-search-preview-2025-03-11'] | Literal['gpt-4o-mini-search-preview-2025-03-11'] | Literal['chatgpt-4o-latest'] | Literal['codex-mini-latest'] | Literal['gpt-4o-mini'] | Literal['gpt-4o-mini-2024-07-18'] | Literal['gpt-4-turbo'] | Literal['gpt-4-turbo-2024-04-09'] | Literal['gpt-4-0125-preview'] | Literal['gpt-4-turbo-preview'] | Literal['gpt-4-1106-preview'] | Literal['gpt-4-vision-preview'] | Literal['gpt-4'] | Literal['gpt-4-0314'] | Literal['gpt-4-0613'] | Literal['gpt-4-32k'] | Literal['gpt-4-32k-0314'] | Literal['gpt-4-32k-0613'] | Literal['gpt-3.5-turbo'] | Literal['gpt-3.5-turbo-16k'] | Literal['gpt-3.5-turbo-0301'] | Literal['gpt-3.5-turbo-0613'] | Literal['gpt-3.5-turbo-1106'] | Literal['gpt-3.5-turbo-0125'] | Literal['gpt-3.5-turbo-16k-0613'] | None | openai._types.NotGiven =, parallel_tool_calls: builtins.bool | openai._types.NotGiven =, response_format: Literal['auto'] | TypedDict('openai.types.shared_params.response_format_text.ResponseFormatText', {'type': Literal['text']}) | TypedDict('openai.types.shared_params.response_format_json_object.ResponseFormatJSONObject', {'type': Literal['json_object']}) | TypedDict('openai.types.shared_params.response_format_json_schema.ResponseFormatJSONSchema', {'json_schema': TypedDict('openai.types.shared_params.response_format_json_schema.JSONSchema', {'name': builtins.str, 'description'?: builtins.str, 'schema'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['json_schema']}) | None | openai._types.NotGiven =, stream: Literal[False] | None | openai._types.NotGiven =, temperature: builtins.float | None | openai._types.NotGiven =, thread: TypedDict('openai.types.beta.thread_create_and_run_params.Thread', {'messages'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessage', {'content': builtins.str | typing.Iterable[TypedDict('openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam', {'image_file': TypedDict('openai.types.beta.threads.image_file_param.ImageFileParam', {'file_id': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_file']}) | TypedDict('openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam', {'image_url': TypedDict('openai.types.beta.threads.image_url_param.ImageURLParam', {'url': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_url']}) | TypedDict('openai.types.beta.threads.text_content_block_param.TextContentBlockParam', {'text': builtins.str, 'type': Literal['text']})], 'role': Literal['user'] | Literal['assistant'], 'attachments'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachment', {'file_id'?: builtins.str, 'tools'?: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachmentToolFileSearch', {'type': Literal['file_search']})]})] | None, 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None, 'tool_resources'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str], 'vector_stores'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStore', {'chunking_strategy'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyAuto', {'type': Literal['auto']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStatic', {'static': TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStaticStatic', {'chunk_overlap_tokens': builtins.int, 'max_chunk_size_tokens': builtins.int}), 'type': Literal['static']}), 'file_ids'?: builtins.list[builtins.str], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})]})}) | None}) | openai._types.NotGiven =, tool_choice: Literal['none'] | Literal['auto'] | Literal['required'] | TypedDict('openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam', {'type': Literal['function'] | Literal['code_interpreter'] | Literal['file_search'], 'function'?: TypedDict('openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam', {'name': builtins.str})}) | None | openai._types.NotGiven =, tool_resources: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str]})}) | None | openai._types.NotGiven =, tools: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.file_search_tool_param.FileSearchToolParam', {'type': Literal['file_search'], 'file_search'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearch', {'max_num_results'?: builtins.int, 'ranking_options'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearchRankingOptions', {'score_threshold': builtins.float, 'ranker'?: Literal['auto'] | Literal['default_2024_08_21']})})}) | TypedDict('openai.types.beta.function_tool_param.FunctionToolParam', {'function': TypedDict('openai.types.shared_params.function_definition.FunctionDefinition', {'name': builtins.str, 'description'?: builtins.str, 'parameters'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['function']})] | None | openai._types.NotGiven =, top_p: builtins.float | None | openai._types.NotGiven =, truncation_strategy: TypedDict('openai.types.beta.thread_create_and_run_params.TruncationStrategy', {'type': Literal['auto'] | Literal['last_messages'], 'last_messages'?: builtins.int | None}) | None | openai._types.NotGiven =, extra_headers: typing.Mapping[builtins.str, builtins.str | openai._types.Omit] | None =, extra_query: typing.Mapping[builtins.str, builtins.object] | None =, extra_body: builtins.object | None =, timeout: builtins.float | httpx._config.Timeout | None | openai._types.NotGiven =) -> typing.Coroutine[Any, Any, openai.types.beta.threads.run.Run] of function openai.resources.beta.threads.threads.AsyncThreads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "overload def (self: openai.resources.beta.threads.threads.AsyncThreads, *, assistant_id: builtins.str, stream: Literal[True], instructions: builtins.str | None | openai._types.NotGiven =, max_completion_tokens: builtins.int | None | openai._types.NotGiven =, max_prompt_tokens: builtins.int | None | openai._types.NotGiven =, metadata: builtins.dict[builtins.str, builtins.str] | None | openai._types.NotGiven =, model: builtins.str | Literal['gpt-4.1'] | Literal['gpt-4.1-mini'] | Literal['gpt-4.1-nano'] | Literal['gpt-4.1-2025-04-14'] | Literal['gpt-4.1-mini-2025-04-14'] | Literal['gpt-4.1-nano-2025-04-14'] | Literal['o4-mini'] | Literal['o4-mini-2025-04-16'] | Literal['o3'] | Literal['o3-2025-04-16'] | Literal['o3-mini'] | Literal['o3-mini-2025-01-31'] | Literal['o1'] | Literal['o1-2024-12-17'] | Literal['o1-preview'] | Literal['o1-preview-2024-09-12'] | Literal['o1-mini'] | Literal['o1-mini-2024-09-12'] | Literal['gpt-4o'] | Literal['gpt-4o-2024-11-20'] | Literal['gpt-4o-2024-08-06'] | Literal['gpt-4o-2024-05-13'] | Literal['gpt-4o-audio-preview'] | Literal['gpt-4o-audio-preview-2024-10-01'] | Literal['gpt-4o-audio-preview-2024-12-17'] | Literal['gpt-4o-audio-preview-2025-06-03'] | Literal['gpt-4o-mini-audio-preview'] | Literal['gpt-4o-mini-audio-preview-2024-12-17'] | Literal['gpt-4o-search-preview'] | Literal['gpt-4o-mini-search-preview'] | Literal['gpt-4o-search-preview-2025-03-11'] | Literal['gpt-4o-mini-search-preview-2025-03-11'] | Literal['chatgpt-4o-latest'] | Literal['codex-mini-latest'] | Literal['gpt-4o-mini'] | Literal['gpt-4o-mini-2024-07-18'] | Literal['gpt-4-turbo'] | Literal['gpt-4-turbo-2024-04-09'] | Literal['gpt-4-0125-preview'] | Literal['gpt-4-turbo-preview'] | Literal['gpt-4-1106-preview'] | Literal['gpt-4-vision-preview'] | Literal['gpt-4'] | Literal['gpt-4-0314'] | Literal['gpt-4-0613'] | Literal['gpt-4-32k'] | Literal['gpt-4-32k-0314'] | Literal['gpt-4-32k-0613'] | Literal['gpt-3.5-turbo'] | Literal['gpt-3.5-turbo-16k'] | Literal['gpt-3.5-turbo-0301'] | Literal['gpt-3.5-turbo-0613'] | Literal['gpt-3.5-turbo-1106'] | Literal['gpt-3.5-turbo-0125'] | Literal['gpt-3.5-turbo-16k-0613'] | None | openai._types.NotGiven =, parallel_tool_calls: builtins.bool | openai._types.NotGiven =, response_format: Literal['auto'] | TypedDict('openai.types.shared_params.response_format_text.ResponseFormatText', {'type': Literal['text']}) | TypedDict('openai.types.shared_params.response_format_json_object.ResponseFormatJSONObject', {'type': Literal['json_object']}) | TypedDict('openai.types.shared_params.response_format_json_schema.ResponseFormatJSONSchema', {'json_schema': TypedDict('openai.types.shared_params.response_format_json_schema.JSONSchema', {'name': builtins.str, 'description'?: builtins.str, 'schema'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['json_schema']}) | None | openai._types.NotGiven =, temperature: builtins.float | None | openai._types.NotGiven =, thread: TypedDict('openai.types.beta.thread_create_and_run_params.Thread', {'messages'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessage', {'content': builtins.str | typing.Iterable[TypedDict('openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam', {'image_file': TypedDict('openai.types.beta.threads.image_file_param.ImageFileParam', {'file_id': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_file']}) | TypedDict('openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam', {'image_url': TypedDict('openai.types.beta.threads.image_url_param.ImageURLParam', {'url': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_url']}) | TypedDict('openai.types.beta.threads.text_content_block_param.TextContentBlockParam', {'text': builtins.str, 'type': Literal['text']})], 'role': Literal['user'] | Literal['assistant'], 'attachments'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachment', {'file_id'?: builtins.str, 'tools'?: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachmentToolFileSearch', {'type': Literal['file_search']})]})] | None, 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None, 'tool_resources'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str], 'vector_stores'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStore', {'chunking_strategy'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyAuto', {'type': Literal['auto']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStatic', {'static': TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStaticStatic', {'chunk_overlap_tokens': builtins.int, 'max_chunk_size_tokens': builtins.int}), 'type': Literal['static']}), 'file_ids'?: builtins.list[builtins.str], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})]})}) | None}) | openai._types.NotGiven =, tool_choice: Literal['none'] | Literal['auto'] | Literal['required'] | TypedDict('openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam', {'type': Literal['function'] | Literal['code_interpreter'] | Literal['file_search'], 'function'?: TypedDict('openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam', {'name': builtins.str})}) | None | openai._types.NotGiven =, tool_resources: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str]})}) | None | openai._types.NotGiven =, tools: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.file_search_tool_param.FileSearchToolParam', {'type': Literal['file_search'], 'file_search'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearch', {'max_num_results'?: builtins.int, 'ranking_options'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearchRankingOptions', {'score_threshold': builtins.float, 'ranker'?: Literal['auto'] | Literal['default_2024_08_21']})})}) | TypedDict('openai.types.beta.function_tool_param.FunctionToolParam', {'function': TypedDict('openai.types.shared_params.function_definition.FunctionDefinition', {'name': builtins.str, 'description'?: builtins.str, 'parameters'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['function']})] | None | openai._types.NotGiven =, top_p: builtins.float | None | openai._types.NotGiven =, truncation_strategy: TypedDict('openai.types.beta.thread_create_and_run_params.TruncationStrategy', {'type': Literal['auto'] | Literal['last_messages'], 'last_messages'?: builtins.int | None}) | None | openai._types.NotGiven =, extra_headers: typing.Mapping[builtins.str, builtins.str | openai._types.Omit] | None =, extra_query: typing.Mapping[builtins.str, builtins.object] | None =, extra_body: builtins.object | None =, timeout: builtins.float | httpx._config.Timeout | None | openai._types.NotGiven =) -> typing.Coroutine[Any, Any, openai._streaming.AsyncStream[openai.types.beta.assistant_stream_event.ThreadCreated | openai.types.beta.assistant_stream_event.ThreadRunCreated | openai.types.beta.assistant_stream_event.ThreadRunQueued | openai.types.beta.assistant_stream_event.ThreadRunInProgress | openai.types.beta.assistant_stream_event.ThreadRunRequiresAction | openai.types.beta.assistant_stream_event.ThreadRunCompleted | openai.types.beta.assistant_stream_event.ThreadRunIncomplete | openai.types.beta.assistant_stream_event.ThreadRunFailed | openai.types.beta.assistant_stream_event.ThreadRunCancelling | openai.types.beta.assistant_stream_event.ThreadRunCancelled | openai.types.beta.assistant_stream_event.ThreadRunExpired | openai.types.beta.assistant_stream_event.ThreadRunStepCreated | openai.types.beta.assistant_stream_event.ThreadRunStepInProgress | openai.types.beta.assistant_stream_event.ThreadRunStepDelta | openai.types.beta.assistant_stream_event.ThreadRunStepCompleted | openai.types.beta.assistant_stream_event.ThreadRunStepFailed | openai.types.beta.assistant_stream_event.ThreadRunStepCancelled | openai.types.beta.assistant_stream_event.ThreadRunStepExpired | openai.types.beta.assistant_stream_event.ThreadMessageCreated | openai.types.beta.assistant_stream_event.ThreadMessageInProgress | openai.types.beta.assistant_stream_event.ThreadMessageDelta | openai.types.beta.assistant_stream_event.ThreadMessageCompleted | openai.types.beta.assistant_stream_event.ThreadMessageIncomplete | openai.types.beta.assistant_stream_event.ErrorEvent]] of function openai.resources.beta.threads.threads.AsyncThreads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "overload def (self: openai.resources.beta.threads.threads.AsyncThreads, *, assistant_id: builtins.str, stream: builtins.bool, instructions: builtins.str | None | openai._types.NotGiven =, max_completion_tokens: builtins.int | None | openai._types.NotGiven =, max_prompt_tokens: builtins.int | None | openai._types.NotGiven =, metadata: builtins.dict[builtins.str, builtins.str] | None | openai._types.NotGiven =, model: builtins.str | Literal['gpt-4.1'] | Literal['gpt-4.1-mini'] | Literal['gpt-4.1-nano'] | Literal['gpt-4.1-2025-04-14'] | Literal['gpt-4.1-mini-2025-04-14'] | Literal['gpt-4.1-nano-2025-04-14'] | Literal['o4-mini'] | Literal['o4-mini-2025-04-16'] | Literal['o3'] | Literal['o3-2025-04-16'] | Literal['o3-mini'] | Literal['o3-mini-2025-01-31'] | Literal['o1'] | Literal['o1-2024-12-17'] | Literal['o1-preview'] | Literal['o1-preview-2024-09-12'] | Literal['o1-mini'] | Literal['o1-mini-2024-09-12'] | Literal['gpt-4o'] | Literal['gpt-4o-2024-11-20'] | Literal['gpt-4o-2024-08-06'] | Literal['gpt-4o-2024-05-13'] | Literal['gpt-4o-audio-preview'] | Literal['gpt-4o-audio-preview-2024-10-01'] | Literal['gpt-4o-audio-preview-2024-12-17'] | Literal['gpt-4o-audio-preview-2025-06-03'] | Literal['gpt-4o-mini-audio-preview'] | Literal['gpt-4o-mini-audio-preview-2024-12-17'] | Literal['gpt-4o-search-preview'] | Literal['gpt-4o-mini-search-preview'] | Literal['gpt-4o-search-preview-2025-03-11'] | Literal['gpt-4o-mini-search-preview-2025-03-11'] | Literal['chatgpt-4o-latest'] | Literal['codex-mini-latest'] | Literal['gpt-4o-mini'] | Literal['gpt-4o-mini-2024-07-18'] | Literal['gpt-4-turbo'] | Literal['gpt-4-turbo-2024-04-09'] | Literal['gpt-4-0125-preview'] | Literal['gpt-4-turbo-preview'] | Literal['gpt-4-1106-preview'] | Literal['gpt-4-vision-preview'] | Literal['gpt-4'] | Literal['gpt-4-0314'] | Literal['gpt-4-0613'] | Literal['gpt-4-32k'] | Literal['gpt-4-32k-0314'] | Literal['gpt-4-32k-0613'] | Literal['gpt-3.5-turbo'] | Literal['gpt-3.5-turbo-16k'] | Literal['gpt-3.5-turbo-0301'] | Literal['gpt-3.5-turbo-0613'] | Literal['gpt-3.5-turbo-1106'] | Literal['gpt-3.5-turbo-0125'] | Literal['gpt-3.5-turbo-16k-0613'] | None | openai._types.NotGiven =, parallel_tool_calls: builtins.bool | openai._types.NotGiven =, response_format: Literal['auto'] | TypedDict('openai.types.shared_params.response_format_text.ResponseFormatText', {'type': Literal['text']}) | TypedDict('openai.types.shared_params.response_format_json_object.ResponseFormatJSONObject', {'type': Literal['json_object']}) | TypedDict('openai.types.shared_params.response_format_json_schema.ResponseFormatJSONSchema', {'json_schema': TypedDict('openai.types.shared_params.response_format_json_schema.JSONSchema', {'name': builtins.str, 'description'?: builtins.str, 'schema'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['json_schema']}) | None | openai._types.NotGiven =, temperature: builtins.float | None | openai._types.NotGiven =, thread: TypedDict('openai.types.beta.thread_create_and_run_params.Thread', {'messages'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessage', {'content': builtins.str | typing.Iterable[TypedDict('openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam', {'image_file': TypedDict('openai.types.beta.threads.image_file_param.ImageFileParam', {'file_id': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_file']}) | TypedDict('openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam', {'image_url': TypedDict('openai.types.beta.threads.image_url_param.ImageURLParam', {'url': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_url']}) | TypedDict('openai.types.beta.threads.text_content_block_param.TextContentBlockParam', {'text': builtins.str, 'type': Literal['text']})], 'role': Literal['user'] | Literal['assistant'], 'attachments'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachment', {'file_id'?: builtins.str, 'tools'?: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachmentToolFileSearch', {'type': Literal['file_search']})]})] | None, 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None, 'tool_resources'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str], 'vector_stores'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStore', {'chunking_strategy'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyAuto', {'type': Literal['auto']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStatic', {'static': TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStaticStatic', {'chunk_overlap_tokens': builtins.int, 'max_chunk_size_tokens': builtins.int}), 'type': Literal['static']}), 'file_ids'?: builtins.list[builtins.str], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})]})}) | None}) | openai._types.NotGiven =, tool_choice: Literal['none'] | Literal['auto'] | Literal['required'] | TypedDict('openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam', {'type': Literal['function'] | Literal['code_interpreter'] | Literal['file_search'], 'function'?: TypedDict('openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam', {'name': builtins.str})}) | None | openai._types.NotGiven =, tool_resources: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str]})}) | None | openai._types.NotGiven =, tools: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.file_search_tool_param.FileSearchToolParam', {'type': Literal['file_search'], 'file_search'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearch', {'max_num_results'?: builtins.int, 'ranking_options'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearchRankingOptions', {'score_threshold': builtins.float, 'ranker'?: Literal['auto'] | Literal['default_2024_08_21']})})}) | TypedDict('openai.types.beta.function_tool_param.FunctionToolParam', {'function': TypedDict('openai.types.shared_params.function_definition.FunctionDefinition', {'name': builtins.str, 'description'?: builtins.str, 'parameters'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['function']})] | None | openai._types.NotGiven =, top_p: builtins.float | None | openai._types.NotGiven =, truncation_strategy: TypedDict('openai.types.beta.thread_create_and_run_params.TruncationStrategy', {'type': Literal['auto'] | Literal['last_messages'], 'last_messages'?: builtins.int | None}) | None | openai._types.NotGiven =, extra_headers: typing.Mapping[builtins.str, builtins.str | openai._types.Omit] | None =, extra_query: typing.Mapping[builtins.str, builtins.object] | None =, extra_body: builtins.object | None =, timeout: builtins.float | httpx._config.Timeout | None | openai._types.NotGiven =) -> typing.Coroutine[Any, Any, openai.types.beta.threads.run.Run | openai._streaming.AsyncStream[openai.types.beta.assistant_stream_event.ThreadCreated | openai.types.beta.assistant_stream_event.ThreadRunCreated | openai.types.beta.assistant_stream_event.ThreadRunQueued | openai.types.beta.assistant_stream_event.ThreadRunInProgress | openai.types.beta.assistant_stream_event.ThreadRunRequiresAction | openai.types.beta.assistant_stream_event.ThreadRunCompleted | openai.types.beta.assistant_stream_event.ThreadRunIncomplete | openai.types.beta.assistant_stream_event.ThreadRunFailed | openai.types.beta.assistant_stream_event.ThreadRunCancelling | openai.types.beta.assistant_stream_event.ThreadRunCancelled | openai.types.beta.assistant_stream_event.ThreadRunExpired | openai.types.beta.assistant_stream_event.ThreadRunStepCreated | openai.types.beta.assistant_stream_event.ThreadRunStepInProgress | openai.types.beta.assistant_stream_event.ThreadRunStepDelta | openai.types.beta.assistant_stream_event.ThreadRunStepCompleted | openai.types.beta.assistant_stream_event.ThreadRunStepFailed | openai.types.beta.assistant_stream_event.ThreadRunStepCancelled | openai.types.beta.assistant_stream_event.ThreadRunStepExpired | openai.types.beta.assistant_stream_event.ThreadMessageCreated | openai.types.beta.assistant_stream_event.ThreadMessageInProgress | openai.types.beta.assistant_stream_event.ThreadMessageDelta | openai.types.beta.assistant_stream_event.ThreadMessageCompleted | openai.types.beta.assistant_stream_event.ThreadMessageIncomplete | openai.types.beta.assistant_stream_event.ErrorEvent]] of function openai.resources.beta.threads.threads.AsyncThreads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "create_and_run_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "poll_interval_ms", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_poll", "name": "create_and_run_poll", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "poll_interval_ms", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_poll of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_run_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "name": "create_and_run_stream", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "name": "create_and_run_stream", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "Instance", "args": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "name": "create_and_run_stream", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "Instance", "args": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "name": "create_and_run_stream", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream", "name": "create_and_run_stream", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "Instance", "args": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": -1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.resources.beta.threads.threads.AsyncThreads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}]}]}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.AsyncThreads.delete is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread_deleted.ThreadDeleted"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread_deleted.ThreadDeleted"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of AsyncThreads", "ret_type": "openai.resources.beta.threads.messages.AsyncMessages", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.messages", "name": "messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of AsyncThreads", "ret_type": "openai.resources.beta.threads.messages.AsyncMessages", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.AsyncThreads.retrieve is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncThreads", "ret_type": "openai.resources.beta.threads.runs.runs.AsyncRuns", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncThreads", "ret_type": "openai.resources.beta.threads.runs.runs.AsyncRuns", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.AsyncThreads.update is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of AsyncThreads", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncThreads", "ret_type": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncThreads", "ret_type": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncThreads", "ret_type": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreads.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncThreads", "ret_type": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.threads.threads.AsyncThreads.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.threads.threads.AsyncThreads", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncThreadsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "name": "AsyncThreadsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.threads.threads", "mro": ["openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncThreadsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse._threads", "name": "_threads", "setter_type": null, "type": "openai.resources.beta.threads.threads.AsyncThreads"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.assistant_stream_event.ThreadCreated", "openai.types.beta.assistant_stream_event.ThreadRunCreated", "openai.types.beta.assistant_stream_event.ThreadRunQueued", "openai.types.beta.assistant_stream_event.ThreadRunInProgress", "openai.types.beta.assistant_stream_event.ThreadRunRequiresAction", "openai.types.beta.assistant_stream_event.ThreadRunCompleted", "openai.types.beta.assistant_stream_event.ThreadRunIncomplete", "openai.types.beta.assistant_stream_event.ThreadRunFailed", "openai.types.beta.assistant_stream_event.ThreadRunCancelling", "openai.types.beta.assistant_stream_event.ThreadRunCancelled", "openai.types.beta.assistant_stream_event.ThreadRunExpired", "openai.types.beta.assistant_stream_event.ThreadRunStepCreated", "openai.types.beta.assistant_stream_event.ThreadRunStepInProgress", "openai.types.beta.assistant_stream_event.ThreadRunStepDelta", "openai.types.beta.assistant_stream_event.ThreadRunStepCompleted", "openai.types.beta.assistant_stream_event.ThreadRunStepFailed", "openai.types.beta.assistant_stream_event.ThreadRunStepCancelled", "openai.types.beta.assistant_stream_event.ThreadRunStepExpired", "openai.types.beta.assistant_stream_event.ThreadMessageCreated", "openai.types.beta.assistant_stream_event.ThreadMessageInProgress", "openai.types.beta.assistant_stream_event.ThreadMessageDelta", "openai.types.beta.assistant_stream_event.ThreadMessageCompleted", "openai.types.beta.assistant_stream_event.ThreadMessageIncomplete", "openai.types.beta.assistant_stream_event.ErrorEvent"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread_deleted.ThreadDeleted"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of AsyncThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.messages.AsyncMessagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.messages", "name": "messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of AsyncThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.messages.AsyncMessagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.runs.runs.AsyncRunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.runs.runs.AsyncRunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncThreadsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "name": "AsyncThreadsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.threads.threads", "mro": ["openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "openai.resources.beta.threads.threads.AsyncThreads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncThreadsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse._threads", "name": "_threads", "setter_type": null, "type": "openai.resources.beta.threads.threads.AsyncThreads"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.assistant_stream_event.ThreadCreated", "openai.types.beta.assistant_stream_event.ThreadRunCreated", "openai.types.beta.assistant_stream_event.ThreadRunQueued", "openai.types.beta.assistant_stream_event.ThreadRunInProgress", "openai.types.beta.assistant_stream_event.ThreadRunRequiresAction", "openai.types.beta.assistant_stream_event.ThreadRunCompleted", "openai.types.beta.assistant_stream_event.ThreadRunIncomplete", "openai.types.beta.assistant_stream_event.ThreadRunFailed", "openai.types.beta.assistant_stream_event.ThreadRunCancelling", "openai.types.beta.assistant_stream_event.ThreadRunCancelled", "openai.types.beta.assistant_stream_event.ThreadRunExpired", "openai.types.beta.assistant_stream_event.ThreadRunStepCreated", "openai.types.beta.assistant_stream_event.ThreadRunStepInProgress", "openai.types.beta.assistant_stream_event.ThreadRunStepDelta", "openai.types.beta.assistant_stream_event.ThreadRunStepCompleted", "openai.types.beta.assistant_stream_event.ThreadRunStepFailed", "openai.types.beta.assistant_stream_event.ThreadRunStepCancelled", "openai.types.beta.assistant_stream_event.ThreadRunStepExpired", "openai.types.beta.assistant_stream_event.ThreadMessageCreated", "openai.types.beta.assistant_stream_event.ThreadMessageInProgress", "openai.types.beta.assistant_stream_event.ThreadMessageDelta", "openai.types.beta.assistant_stream_event.ThreadMessageCompleted", "openai.types.beta.assistant_stream_event.ThreadMessageIncomplete", "openai.types.beta.assistant_stream_event.ErrorEvent"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread_deleted.ThreadDeleted"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of AsyncThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.messages.AsyncMessagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.messages", "name": "messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of AsyncThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.messages.AsyncMessagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.runs.runs.AsyncRunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.runs.runs.AsyncRunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Body": {".class": "SymbolTableNode", "cross_ref": "openai._types.Body", "kind": "Gdef", "module_public": false}, "ChatModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.chat_model.ChatModel", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Messages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.Messages", "kind": "Gdef", "module_public": false}, "MessagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.MessagesWithRawResponse", "kind": "Gdef", "module_public": false}, "MessagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.MessagesWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared_params.metadata.Metadata", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "Run": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run.Run", "kind": "Gdef", "module_public": false}, "Runs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.Runs", "kind": "Gdef", "module_public": false}, "RunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.RunsWithRawResponse", "kind": "Gdef", "module_public": false}, "RunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.RunsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Stream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.Stream", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "Thread": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread.Thread", "kind": "Gdef", "module_public": false}, "ThreadDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_deleted.ThreadDeleted", "kind": "Gdef", "module_public": false}, "Threads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.threads.threads.Threads", "name": "Threads", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.Threads", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.threads.threads", "mro": ["openai.resources.beta.threads.threads.Threads", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.Threads.create is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Threads", "ret_type": "openai.types.beta.thread.Thread", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Threads", "ret_type": "openai.types.beta.thread.Thread", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_and_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": "function openai.resources.beta.threads.threads.Threads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": [], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.Threads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "overload def (self: openai.resources.beta.threads.threads.Threads, *, assistant_id: builtins.str, instructions: builtins.str | None | openai._types.NotGiven =, max_completion_tokens: builtins.int | None | openai._types.NotGiven =, max_prompt_tokens: builtins.int | None | openai._types.NotGiven =, metadata: builtins.dict[builtins.str, builtins.str] | None | openai._types.NotGiven =, model: builtins.str | Literal['gpt-4.1'] | Literal['gpt-4.1-mini'] | Literal['gpt-4.1-nano'] | Literal['gpt-4.1-2025-04-14'] | Literal['gpt-4.1-mini-2025-04-14'] | Literal['gpt-4.1-nano-2025-04-14'] | Literal['o4-mini'] | Literal['o4-mini-2025-04-16'] | Literal['o3'] | Literal['o3-2025-04-16'] | Literal['o3-mini'] | Literal['o3-mini-2025-01-31'] | Literal['o1'] | Literal['o1-2024-12-17'] | Literal['o1-preview'] | Literal['o1-preview-2024-09-12'] | Literal['o1-mini'] | Literal['o1-mini-2024-09-12'] | Literal['gpt-4o'] | Literal['gpt-4o-2024-11-20'] | Literal['gpt-4o-2024-08-06'] | Literal['gpt-4o-2024-05-13'] | Literal['gpt-4o-audio-preview'] | Literal['gpt-4o-audio-preview-2024-10-01'] | Literal['gpt-4o-audio-preview-2024-12-17'] | Literal['gpt-4o-audio-preview-2025-06-03'] | Literal['gpt-4o-mini-audio-preview'] | Literal['gpt-4o-mini-audio-preview-2024-12-17'] | Literal['gpt-4o-search-preview'] | Literal['gpt-4o-mini-search-preview'] | Literal['gpt-4o-search-preview-2025-03-11'] | Literal['gpt-4o-mini-search-preview-2025-03-11'] | Literal['chatgpt-4o-latest'] | Literal['codex-mini-latest'] | Literal['gpt-4o-mini'] | Literal['gpt-4o-mini-2024-07-18'] | Literal['gpt-4-turbo'] | Literal['gpt-4-turbo-2024-04-09'] | Literal['gpt-4-0125-preview'] | Literal['gpt-4-turbo-preview'] | Literal['gpt-4-1106-preview'] | Literal['gpt-4-vision-preview'] | Literal['gpt-4'] | Literal['gpt-4-0314'] | Literal['gpt-4-0613'] | Literal['gpt-4-32k'] | Literal['gpt-4-32k-0314'] | Literal['gpt-4-32k-0613'] | Literal['gpt-3.5-turbo'] | Literal['gpt-3.5-turbo-16k'] | Literal['gpt-3.5-turbo-0301'] | Literal['gpt-3.5-turbo-0613'] | Literal['gpt-3.5-turbo-1106'] | Literal['gpt-3.5-turbo-0125'] | Literal['gpt-3.5-turbo-16k-0613'] | None | openai._types.NotGiven =, parallel_tool_calls: builtins.bool | openai._types.NotGiven =, response_format: Literal['auto'] | TypedDict('openai.types.shared_params.response_format_text.ResponseFormatText', {'type': Literal['text']}) | TypedDict('openai.types.shared_params.response_format_json_object.ResponseFormatJSONObject', {'type': Literal['json_object']}) | TypedDict('openai.types.shared_params.response_format_json_schema.ResponseFormatJSONSchema', {'json_schema': TypedDict('openai.types.shared_params.response_format_json_schema.JSONSchema', {'name': builtins.str, 'description'?: builtins.str, 'schema'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['json_schema']}) | None | openai._types.NotGiven =, stream: Literal[False] | None | openai._types.NotGiven =, temperature: builtins.float | None | openai._types.NotGiven =, thread: TypedDict('openai.types.beta.thread_create_and_run_params.Thread', {'messages'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessage', {'content': builtins.str | typing.Iterable[TypedDict('openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam', {'image_file': TypedDict('openai.types.beta.threads.image_file_param.ImageFileParam', {'file_id': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_file']}) | TypedDict('openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam', {'image_url': TypedDict('openai.types.beta.threads.image_url_param.ImageURLParam', {'url': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_url']}) | TypedDict('openai.types.beta.threads.text_content_block_param.TextContentBlockParam', {'text': builtins.str, 'type': Literal['text']})], 'role': Literal['user'] | Literal['assistant'], 'attachments'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachment', {'file_id'?: builtins.str, 'tools'?: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachmentToolFileSearch', {'type': Literal['file_search']})]})] | None, 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None, 'tool_resources'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str], 'vector_stores'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStore', {'chunking_strategy'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyAuto', {'type': Literal['auto']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStatic', {'static': TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStaticStatic', {'chunk_overlap_tokens': builtins.int, 'max_chunk_size_tokens': builtins.int}), 'type': Literal['static']}), 'file_ids'?: builtins.list[builtins.str], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})]})}) | None}) | openai._types.NotGiven =, tool_choice: Literal['none'] | Literal['auto'] | Literal['required'] | TypedDict('openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam', {'type': Literal['function'] | Literal['code_interpreter'] | Literal['file_search'], 'function'?: TypedDict('openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam', {'name': builtins.str})}) | None | openai._types.NotGiven =, tool_resources: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str]})}) | None | openai._types.NotGiven =, tools: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.file_search_tool_param.FileSearchToolParam', {'type': Literal['file_search'], 'file_search'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearch', {'max_num_results'?: builtins.int, 'ranking_options'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearchRankingOptions', {'score_threshold': builtins.float, 'ranker'?: Literal['auto'] | Literal['default_2024_08_21']})})}) | TypedDict('openai.types.beta.function_tool_param.FunctionToolParam', {'function': TypedDict('openai.types.shared_params.function_definition.FunctionDefinition', {'name': builtins.str, 'description'?: builtins.str, 'parameters'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['function']})] | None | openai._types.NotGiven =, top_p: builtins.float | None | openai._types.NotGiven =, truncation_strategy: TypedDict('openai.types.beta.thread_create_and_run_params.TruncationStrategy', {'type': Literal['auto'] | Literal['last_messages'], 'last_messages'?: builtins.int | None}) | None | openai._types.NotGiven =, extra_headers: typing.Mapping[builtins.str, builtins.str | openai._types.Omit] | None =, extra_query: typing.Mapping[builtins.str, builtins.object] | None =, extra_body: builtins.object | None =, timeout: builtins.float | httpx._config.Timeout | None | openai._types.NotGiven =) -> openai.types.beta.threads.run.Run of function openai.resources.beta.threads.threads.Threads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": "openai.types.beta.threads.run.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": "openai.types.beta.threads.run.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "overload def (self: openai.resources.beta.threads.threads.Threads, *, assistant_id: builtins.str, stream: Literal[True], instructions: builtins.str | None | openai._types.NotGiven =, max_completion_tokens: builtins.int | None | openai._types.NotGiven =, max_prompt_tokens: builtins.int | None | openai._types.NotGiven =, metadata: builtins.dict[builtins.str, builtins.str] | None | openai._types.NotGiven =, model: builtins.str | Literal['gpt-4.1'] | Literal['gpt-4.1-mini'] | Literal['gpt-4.1-nano'] | Literal['gpt-4.1-2025-04-14'] | Literal['gpt-4.1-mini-2025-04-14'] | Literal['gpt-4.1-nano-2025-04-14'] | Literal['o4-mini'] | Literal['o4-mini-2025-04-16'] | Literal['o3'] | Literal['o3-2025-04-16'] | Literal['o3-mini'] | Literal['o3-mini-2025-01-31'] | Literal['o1'] | Literal['o1-2024-12-17'] | Literal['o1-preview'] | Literal['o1-preview-2024-09-12'] | Literal['o1-mini'] | Literal['o1-mini-2024-09-12'] | Literal['gpt-4o'] | Literal['gpt-4o-2024-11-20'] | Literal['gpt-4o-2024-08-06'] | Literal['gpt-4o-2024-05-13'] | Literal['gpt-4o-audio-preview'] | Literal['gpt-4o-audio-preview-2024-10-01'] | Literal['gpt-4o-audio-preview-2024-12-17'] | Literal['gpt-4o-audio-preview-2025-06-03'] | Literal['gpt-4o-mini-audio-preview'] | Literal['gpt-4o-mini-audio-preview-2024-12-17'] | Literal['gpt-4o-search-preview'] | Literal['gpt-4o-mini-search-preview'] | Literal['gpt-4o-search-preview-2025-03-11'] | Literal['gpt-4o-mini-search-preview-2025-03-11'] | Literal['chatgpt-4o-latest'] | Literal['codex-mini-latest'] | Literal['gpt-4o-mini'] | Literal['gpt-4o-mini-2024-07-18'] | Literal['gpt-4-turbo'] | Literal['gpt-4-turbo-2024-04-09'] | Literal['gpt-4-0125-preview'] | Literal['gpt-4-turbo-preview'] | Literal['gpt-4-1106-preview'] | Literal['gpt-4-vision-preview'] | Literal['gpt-4'] | Literal['gpt-4-0314'] | Literal['gpt-4-0613'] | Literal['gpt-4-32k'] | Literal['gpt-4-32k-0314'] | Literal['gpt-4-32k-0613'] | Literal['gpt-3.5-turbo'] | Literal['gpt-3.5-turbo-16k'] | Literal['gpt-3.5-turbo-0301'] | Literal['gpt-3.5-turbo-0613'] | Literal['gpt-3.5-turbo-1106'] | Literal['gpt-3.5-turbo-0125'] | Literal['gpt-3.5-turbo-16k-0613'] | None | openai._types.NotGiven =, parallel_tool_calls: builtins.bool | openai._types.NotGiven =, response_format: Literal['auto'] | TypedDict('openai.types.shared_params.response_format_text.ResponseFormatText', {'type': Literal['text']}) | TypedDict('openai.types.shared_params.response_format_json_object.ResponseFormatJSONObject', {'type': Literal['json_object']}) | TypedDict('openai.types.shared_params.response_format_json_schema.ResponseFormatJSONSchema', {'json_schema': TypedDict('openai.types.shared_params.response_format_json_schema.JSONSchema', {'name': builtins.str, 'description'?: builtins.str, 'schema'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['json_schema']}) | None | openai._types.NotGiven =, temperature: builtins.float | None | openai._types.NotGiven =, thread: TypedDict('openai.types.beta.thread_create_and_run_params.Thread', {'messages'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessage', {'content': builtins.str | typing.Iterable[TypedDict('openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam', {'image_file': TypedDict('openai.types.beta.threads.image_file_param.ImageFileParam', {'file_id': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_file']}) | TypedDict('openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam', {'image_url': TypedDict('openai.types.beta.threads.image_url_param.ImageURLParam', {'url': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_url']}) | TypedDict('openai.types.beta.threads.text_content_block_param.TextContentBlockParam', {'text': builtins.str, 'type': Literal['text']})], 'role': Literal['user'] | Literal['assistant'], 'attachments'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachment', {'file_id'?: builtins.str, 'tools'?: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachmentToolFileSearch', {'type': Literal['file_search']})]})] | None, 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None, 'tool_resources'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str], 'vector_stores'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStore', {'chunking_strategy'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyAuto', {'type': Literal['auto']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStatic', {'static': TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStaticStatic', {'chunk_overlap_tokens': builtins.int, 'max_chunk_size_tokens': builtins.int}), 'type': Literal['static']}), 'file_ids'?: builtins.list[builtins.str], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})]})}) | None}) | openai._types.NotGiven =, tool_choice: Literal['none'] | Literal['auto'] | Literal['required'] | TypedDict('openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam', {'type': Literal['function'] | Literal['code_interpreter'] | Literal['file_search'], 'function'?: TypedDict('openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam', {'name': builtins.str})}) | None | openai._types.NotGiven =, tool_resources: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str]})}) | None | openai._types.NotGiven =, tools: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.file_search_tool_param.FileSearchToolParam', {'type': Literal['file_search'], 'file_search'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearch', {'max_num_results'?: builtins.int, 'ranking_options'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearchRankingOptions', {'score_threshold': builtins.float, 'ranker'?: Literal['auto'] | Literal['default_2024_08_21']})})}) | TypedDict('openai.types.beta.function_tool_param.FunctionToolParam', {'function': TypedDict('openai.types.shared_params.function_definition.FunctionDefinition', {'name': builtins.str, 'description'?: builtins.str, 'parameters'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['function']})] | None | openai._types.NotGiven =, top_p: builtins.float | None | openai._types.NotGiven =, truncation_strategy: TypedDict('openai.types.beta.thread_create_and_run_params.TruncationStrategy', {'type': Literal['auto'] | Literal['last_messages'], 'last_messages'?: builtins.int | None}) | None | openai._types.NotGiven =, extra_headers: typing.Mapping[builtins.str, builtins.str | openai._types.Omit] | None =, extra_query: typing.Mapping[builtins.str, builtins.object] | None =, extra_body: builtins.object | None =, timeout: builtins.float | httpx._config.Timeout | None | openai._types.NotGiven =) -> openai._streaming.Stream[openai.types.beta.assistant_stream_event.ThreadCreated | openai.types.beta.assistant_stream_event.ThreadRunCreated | openai.types.beta.assistant_stream_event.ThreadRunQueued | openai.types.beta.assistant_stream_event.ThreadRunInProgress | openai.types.beta.assistant_stream_event.ThreadRunRequiresAction | openai.types.beta.assistant_stream_event.ThreadRunCompleted | openai.types.beta.assistant_stream_event.ThreadRunIncomplete | openai.types.beta.assistant_stream_event.ThreadRunFailed | openai.types.beta.assistant_stream_event.ThreadRunCancelling | openai.types.beta.assistant_stream_event.ThreadRunCancelled | openai.types.beta.assistant_stream_event.ThreadRunExpired | openai.types.beta.assistant_stream_event.ThreadRunStepCreated | openai.types.beta.assistant_stream_event.ThreadRunStepInProgress | openai.types.beta.assistant_stream_event.ThreadRunStepDelta | openai.types.beta.assistant_stream_event.ThreadRunStepCompleted | openai.types.beta.assistant_stream_event.ThreadRunStepFailed | openai.types.beta.assistant_stream_event.ThreadRunStepCancelled | openai.types.beta.assistant_stream_event.ThreadRunStepExpired | openai.types.beta.assistant_stream_event.ThreadMessageCreated | openai.types.beta.assistant_stream_event.ThreadMessageInProgress | openai.types.beta.assistant_stream_event.ThreadMessageDelta | openai.types.beta.assistant_stream_event.ThreadMessageCompleted | openai.types.beta.assistant_stream_event.ThreadMessageIncomplete | openai.types.beta.assistant_stream_event.ErrorEvent] of function openai.resources.beta.threads.threads.Threads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "overload def (self: openai.resources.beta.threads.threads.Threads, *, assistant_id: builtins.str, stream: builtins.bool, instructions: builtins.str | None | openai._types.NotGiven =, max_completion_tokens: builtins.int | None | openai._types.NotGiven =, max_prompt_tokens: builtins.int | None | openai._types.NotGiven =, metadata: builtins.dict[builtins.str, builtins.str] | None | openai._types.NotGiven =, model: builtins.str | Literal['gpt-4.1'] | Literal['gpt-4.1-mini'] | Literal['gpt-4.1-nano'] | Literal['gpt-4.1-2025-04-14'] | Literal['gpt-4.1-mini-2025-04-14'] | Literal['gpt-4.1-nano-2025-04-14'] | Literal['o4-mini'] | Literal['o4-mini-2025-04-16'] | Literal['o3'] | Literal['o3-2025-04-16'] | Literal['o3-mini'] | Literal['o3-mini-2025-01-31'] | Literal['o1'] | Literal['o1-2024-12-17'] | Literal['o1-preview'] | Literal['o1-preview-2024-09-12'] | Literal['o1-mini'] | Literal['o1-mini-2024-09-12'] | Literal['gpt-4o'] | Literal['gpt-4o-2024-11-20'] | Literal['gpt-4o-2024-08-06'] | Literal['gpt-4o-2024-05-13'] | Literal['gpt-4o-audio-preview'] | Literal['gpt-4o-audio-preview-2024-10-01'] | Literal['gpt-4o-audio-preview-2024-12-17'] | Literal['gpt-4o-audio-preview-2025-06-03'] | Literal['gpt-4o-mini-audio-preview'] | Literal['gpt-4o-mini-audio-preview-2024-12-17'] | Literal['gpt-4o-search-preview'] | Literal['gpt-4o-mini-search-preview'] | Literal['gpt-4o-search-preview-2025-03-11'] | Literal['gpt-4o-mini-search-preview-2025-03-11'] | Literal['chatgpt-4o-latest'] | Literal['codex-mini-latest'] | Literal['gpt-4o-mini'] | Literal['gpt-4o-mini-2024-07-18'] | Literal['gpt-4-turbo'] | Literal['gpt-4-turbo-2024-04-09'] | Literal['gpt-4-0125-preview'] | Literal['gpt-4-turbo-preview'] | Literal['gpt-4-1106-preview'] | Literal['gpt-4-vision-preview'] | Literal['gpt-4'] | Literal['gpt-4-0314'] | Literal['gpt-4-0613'] | Literal['gpt-4-32k'] | Literal['gpt-4-32k-0314'] | Literal['gpt-4-32k-0613'] | Literal['gpt-3.5-turbo'] | Literal['gpt-3.5-turbo-16k'] | Literal['gpt-3.5-turbo-0301'] | Literal['gpt-3.5-turbo-0613'] | Literal['gpt-3.5-turbo-1106'] | Literal['gpt-3.5-turbo-0125'] | Literal['gpt-3.5-turbo-16k-0613'] | None | openai._types.NotGiven =, parallel_tool_calls: builtins.bool | openai._types.NotGiven =, response_format: Literal['auto'] | TypedDict('openai.types.shared_params.response_format_text.ResponseFormatText', {'type': Literal['text']}) | TypedDict('openai.types.shared_params.response_format_json_object.ResponseFormatJSONObject', {'type': Literal['json_object']}) | TypedDict('openai.types.shared_params.response_format_json_schema.ResponseFormatJSONSchema', {'json_schema': TypedDict('openai.types.shared_params.response_format_json_schema.JSONSchema', {'name': builtins.str, 'description'?: builtins.str, 'schema'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['json_schema']}) | None | openai._types.NotGiven =, temperature: builtins.float | None | openai._types.NotGiven =, thread: TypedDict('openai.types.beta.thread_create_and_run_params.Thread', {'messages'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessage', {'content': builtins.str | typing.Iterable[TypedDict('openai.types.beta.threads.image_file_content_block_param.ImageFileContentBlockParam', {'image_file': TypedDict('openai.types.beta.threads.image_file_param.ImageFileParam', {'file_id': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_file']}) | TypedDict('openai.types.beta.threads.image_url_content_block_param.ImageURLContentBlockParam', {'image_url': TypedDict('openai.types.beta.threads.image_url_param.ImageURLParam', {'url': builtins.str, 'detail'?: Literal['auto'] | Literal['low'] | Literal['high']}), 'type': Literal['image_url']}) | TypedDict('openai.types.beta.threads.text_content_block_param.TextContentBlockParam', {'text': builtins.str, 'type': Literal['text']})], 'role': Literal['user'] | Literal['assistant'], 'attachments'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachment', {'file_id'?: builtins.str, 'tools'?: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadMessageAttachmentToolFileSearch', {'type': Literal['file_search']})]})] | None, 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None, 'tool_resources'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str], 'vector_stores'?: typing.Iterable[TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStore', {'chunking_strategy'?: TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyAuto', {'type': Literal['auto']}) | TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStatic', {'static': TypedDict('openai.types.beta.thread_create_and_run_params.ThreadToolResourcesFileSearchVectorStoreChunkingStrategyStaticStatic', {'chunk_overlap_tokens': builtins.int, 'max_chunk_size_tokens': builtins.int}), 'type': Literal['static']}), 'file_ids'?: builtins.list[builtins.str], 'metadata'?: builtins.dict[builtins.str, builtins.str] | None})]})}) | None}) | openai._types.NotGiven =, tool_choice: Literal['none'] | Literal['auto'] | Literal['required'] | TypedDict('openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam', {'type': Literal['function'] | Literal['code_interpreter'] | Literal['file_search'], 'function'?: TypedDict('openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam', {'name': builtins.str})}) | None | openai._types.NotGiven =, tool_resources: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResources', {'code_interpreter'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesCodeInterpreter', {'file_ids'?: builtins.list[builtins.str]}), 'file_search'?: TypedDict('openai.types.beta.thread_create_and_run_params.ToolResourcesFileSearch', {'vector_store_ids'?: builtins.list[builtins.str]})}) | None | openai._types.NotGiven =, tools: typing.Iterable[TypedDict('openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam', {'type': Literal['code_interpreter']}) | TypedDict('openai.types.beta.file_search_tool_param.FileSearchToolParam', {'type': Literal['file_search'], 'file_search'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearch', {'max_num_results'?: builtins.int, 'ranking_options'?: TypedDict('openai.types.beta.file_search_tool_param.FileSearchRankingOptions', {'score_threshold': builtins.float, 'ranker'?: Literal['auto'] | Literal['default_2024_08_21']})})}) | TypedDict('openai.types.beta.function_tool_param.FunctionToolParam', {'function': TypedDict('openai.types.shared_params.function_definition.FunctionDefinition', {'name': builtins.str, 'description'?: builtins.str, 'parameters'?: builtins.dict[builtins.str, builtins.object], 'strict'?: builtins.bool | None}), 'type': Literal['function']})] | None | openai._types.NotGiven =, top_p: builtins.float | None | openai._types.NotGiven =, truncation_strategy: TypedDict('openai.types.beta.thread_create_and_run_params.TruncationStrategy', {'type': Literal['auto'] | Literal['last_messages'], 'last_messages'?: builtins.int | None}) | None | openai._types.NotGiven =, extra_headers: typing.Mapping[builtins.str, builtins.str | openai._types.Omit] | None =, extra_query: typing.Mapping[builtins.str, builtins.object] | None =, extra_body: builtins.object | None =, timeout: builtins.float | httpx._config.Timeout | None | openai._types.NotGiven =) -> openai.types.beta.threads.run.Run | openai._streaming.Stream[openai.types.beta.assistant_stream_event.ThreadCreated | openai.types.beta.assistant_stream_event.ThreadRunCreated | openai.types.beta.assistant_stream_event.ThreadRunQueued | openai.types.beta.assistant_stream_event.ThreadRunInProgress | openai.types.beta.assistant_stream_event.ThreadRunRequiresAction | openai.types.beta.assistant_stream_event.ThreadRunCompleted | openai.types.beta.assistant_stream_event.ThreadRunIncomplete | openai.types.beta.assistant_stream_event.ThreadRunFailed | openai.types.beta.assistant_stream_event.ThreadRunCancelling | openai.types.beta.assistant_stream_event.ThreadRunCancelled | openai.types.beta.assistant_stream_event.ThreadRunExpired | openai.types.beta.assistant_stream_event.ThreadRunStepCreated | openai.types.beta.assistant_stream_event.ThreadRunStepInProgress | openai.types.beta.assistant_stream_event.ThreadRunStepDelta | openai.types.beta.assistant_stream_event.ThreadRunStepCompleted | openai.types.beta.assistant_stream_event.ThreadRunStepFailed | openai.types.beta.assistant_stream_event.ThreadRunStepCancelled | openai.types.beta.assistant_stream_event.ThreadRunStepExpired | openai.types.beta.assistant_stream_event.ThreadMessageCreated | openai.types.beta.assistant_stream_event.ThreadMessageInProgress | openai.types.beta.assistant_stream_event.ThreadMessageDelta | openai.types.beta.assistant_stream_event.ThreadMessageCompleted | openai.types.beta.assistant_stream_event.ThreadMessageIncomplete | openai.types.beta.assistant_stream_event.ErrorEvent] of function openai.resources.beta.threads.threads.Threads.create_and_run is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": "openai.types.beta.threads.run.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run of Threads", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "create_and_run_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "poll_interval_ms", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_poll", "name": "create_and_run_poll", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "poll_interval_ms", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_poll of Threads", "ret_type": "openai.types.beta.threads.run.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_run_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "name": "create_and_run_stream", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "name": "create_and_run_stream", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "Instance", "args": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "name": "create_and_run_stream", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "Instance", "args": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "name": "create_and_run_stream", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.create_and_run_stream", "name": "create_and_run_stream", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "Instance", "args": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["self", "assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "event_handler", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_run_stream of Threads", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": -1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.resources.beta.threads.threads.Threads.create_and_run_stream#1", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}]}]}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.Threads.delete is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Threads", "ret_type": "openai.types.beta.thread_deleted.ThreadDeleted", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Threads", "ret_type": "openai.types.beta.thread_deleted.ThreadDeleted", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of Threads", "ret_type": "openai.resources.beta.threads.messages.Messages", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.messages", "name": "messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of Threads", "ret_type": "openai.resources.beta.threads.messages.Messages", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.Threads.retrieve is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of Threads", "ret_type": "openai.types.beta.thread.Thread", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of Threads", "ret_type": "openai.types.beta.thread.Thread", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of Threads", "ret_type": "openai.resources.beta.threads.runs.runs.Runs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of Threads", "ret_type": "openai.resources.beta.threads.runs.runs.Runs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": "function openai.resources.beta.threads.threads.Threads.update is deprecated: The Assistants API is deprecated in favor of the Responses API", "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Threads", "ret_type": "openai.types.beta.thread.Thread", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.threads.threads.Threads", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Threads", "ret_type": "openai.types.beta.thread.Thread", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Threads", "ret_type": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Threads", "ret_type": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.Threads.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Threads", "ret_type": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.Threads.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Threads", "ret_type": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.threads.threads.Threads.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.threads.threads.Threads", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThreadsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "name": "ThreadsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.threads.threads", "mro": ["openai.resources.beta.threads.threads.ThreadsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithRawResponse", "openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ThreadsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse._threads", "name": "_threads", "setter_type": null, "type": "openai.resources.beta.threads.threads.Threads"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.assistant_stream_event.ThreadCreated", "openai.types.beta.assistant_stream_event.ThreadRunCreated", "openai.types.beta.assistant_stream_event.ThreadRunQueued", "openai.types.beta.assistant_stream_event.ThreadRunInProgress", "openai.types.beta.assistant_stream_event.ThreadRunRequiresAction", "openai.types.beta.assistant_stream_event.ThreadRunCompleted", "openai.types.beta.assistant_stream_event.ThreadRunIncomplete", "openai.types.beta.assistant_stream_event.ThreadRunFailed", "openai.types.beta.assistant_stream_event.ThreadRunCancelling", "openai.types.beta.assistant_stream_event.ThreadRunCancelled", "openai.types.beta.assistant_stream_event.ThreadRunExpired", "openai.types.beta.assistant_stream_event.ThreadRunStepCreated", "openai.types.beta.assistant_stream_event.ThreadRunStepInProgress", "openai.types.beta.assistant_stream_event.ThreadRunStepDelta", "openai.types.beta.assistant_stream_event.ThreadRunStepCompleted", "openai.types.beta.assistant_stream_event.ThreadRunStepFailed", "openai.types.beta.assistant_stream_event.ThreadRunStepCancelled", "openai.types.beta.assistant_stream_event.ThreadRunStepExpired", "openai.types.beta.assistant_stream_event.ThreadMessageCreated", "openai.types.beta.assistant_stream_event.ThreadMessageInProgress", "openai.types.beta.assistant_stream_event.ThreadMessageDelta", "openai.types.beta.assistant_stream_event.ThreadMessageCompleted", "openai.types.beta.assistant_stream_event.ThreadMessageIncomplete", "openai.types.beta.assistant_stream_event.ErrorEvent"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.beta.thread_deleted.ThreadDeleted"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of ThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.messages.MessagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.messages", "name": "messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of ThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.messages.MessagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of ThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.runs.runs.RunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of ThreadsWithRawResponse", "ret_type": "openai.resources.beta.threads.runs.runs.RunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.threads.threads.ThreadsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThreadsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "name": "ThreadsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.threads.threads", "mro": ["openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "threads"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "openai.resources.beta.threads.threads.Threads"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ThreadsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse._threads", "name": "_threads", "setter_type": null, "type": "openai.resources.beta.threads.threads.Threads"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.Message"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.create_and_run", "name": "create_and_run", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "stream", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.assistant_stream_event.ThreadCreated", "openai.types.beta.assistant_stream_event.ThreadRunCreated", "openai.types.beta.assistant_stream_event.ThreadRunQueued", "openai.types.beta.assistant_stream_event.ThreadRunInProgress", "openai.types.beta.assistant_stream_event.ThreadRunRequiresAction", "openai.types.beta.assistant_stream_event.ThreadRunCompleted", "openai.types.beta.assistant_stream_event.ThreadRunIncomplete", "openai.types.beta.assistant_stream_event.ThreadRunFailed", "openai.types.beta.assistant_stream_event.ThreadRunCancelling", "openai.types.beta.assistant_stream_event.ThreadRunCancelled", "openai.types.beta.assistant_stream_event.ThreadRunExpired", "openai.types.beta.assistant_stream_event.ThreadRunStepCreated", "openai.types.beta.assistant_stream_event.ThreadRunStepInProgress", "openai.types.beta.assistant_stream_event.ThreadRunStepDelta", "openai.types.beta.assistant_stream_event.ThreadRunStepCompleted", "openai.types.beta.assistant_stream_event.ThreadRunStepFailed", "openai.types.beta.assistant_stream_event.ThreadRunStepCancelled", "openai.types.beta.assistant_stream_event.ThreadRunStepExpired", "openai.types.beta.assistant_stream_event.ThreadMessageCreated", "openai.types.beta.assistant_stream_event.ThreadMessageInProgress", "openai.types.beta.assistant_stream_event.ThreadMessageDelta", "openai.types.beta.assistant_stream_event.ThreadMessageCompleted", "openai.types.beta.assistant_stream_event.ThreadMessageIncomplete", "openai.types.beta.assistant_stream_event.ErrorEvent"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["assistant_id", "stream", "instructions", "max_completion_tokens", "max_prompt_tokens", "metadata", "model", "parallel_tool_calls", "response_format", "temperature", "thread", "tool_choice", "tool_resources", "tools", "top_p", "truncation_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.chat_model.ChatModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.Thread"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_create_and_run_params.TruncationStrategy"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread_deleted.ThreadDeleted"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.messages", "name": "messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of ThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.messages.MessagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.messages", "name": "messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages of ThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.messages.MessagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["thread_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of ThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.runs.runs.RunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.threads.threads.ThreadsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of ThreadsWithStreamingResponse", "ret_type": "openai.resources.beta.threads.runs.runs.RunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["thread_id", "metadata", "tool_resources", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.thread_update_params.ToolResources"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object", {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.thread.Thread"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.threads.threads.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.threads.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.threads.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.threads.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.threads.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.threads.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.threads.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_legacy_response": {".class": "SymbolTableNode", "cross_ref": "openai._legacy_response", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "async_to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.async_to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "make_request_options": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.make_request_options", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "required_args": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.required_args", "kind": "Gdef", "module_public": false}, "thread_create_and_run_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_create_and_run_params", "kind": "Gdef", "module_public": false}, "thread_create_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_create_params", "kind": "Gdef", "module_public": false}, "thread_update_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_update_params", "kind": "Gdef", "module_public": false}, "to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\beta\\threads\\threads.py"}