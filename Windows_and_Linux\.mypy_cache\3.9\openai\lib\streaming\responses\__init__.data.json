{".class": "MypyFile", "_fullname": "openai.lib.streaming.responses", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncResponseStream": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream", "kind": "Gdef"}, "AsyncResponseStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef"}, "ResponseStream": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._responses.ResponseStream", "kind": "Gdef"}, "ResponseStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent", "kind": "Gdef"}, "ResponseStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._responses.ResponseStreamManager", "kind": "Gdef"}, "ResponseStreamState": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._responses.ResponseStreamState", "kind": "Gdef"}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent", "kind": "Gdef"}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\__init__.py"}