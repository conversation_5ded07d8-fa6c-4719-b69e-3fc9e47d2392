{".class": "MypyFile", "_fullname": "openai.lib.streaming", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AssistantEventHandler": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "AssistantEventHandlerT": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AssistantEventHandlerT", "kind": "Gdef"}, "AssistantStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AssistantStreamManager", "kind": "Gdef"}, "AsyncAssistantEventHandler": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "kind": "Gdef"}, "AsyncAssistantEventHandlerT": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "kind": "Gdef"}, "AsyncAssistantStreamManager": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\streaming\\__init__.py"}