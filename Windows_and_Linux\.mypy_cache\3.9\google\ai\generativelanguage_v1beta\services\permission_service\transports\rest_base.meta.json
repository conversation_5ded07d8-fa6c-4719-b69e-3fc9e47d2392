{"data_mtime": 1754415050, "dep_lines": [29, 25, 27, 25, 20, 20, 20, 16, 17, 18, 1, 1, 1, 1, 1, 21, 22], "dep_prios": [5, 10, 10, 20, 10, 10, 20, 10, 10, 5, 5, 30, 30, 30, 30, 5, 5], "dependencies": ["google.ai.generativelanguage_v1beta.services.permission_service.transports.base", "google.ai.generativelanguage_v1beta.types.permission", "google.ai.generativelanguage_v1beta.types.permission_service", "google.ai.generativelanguage_v1beta.types", "google.api_core.gapic_v1", "google.api_core.path_template", "google.api_core", "json", "re", "typing", "builtins", "_frozen_importlib", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info"], "hash": "287820ec7cec7c920b13a0c97f50e2f3ff1a708a", "id": "google.ai.generativelanguage_v1beta.services.permission_service.transports.rest_base", "ignore_all": true, "interface_hash": "1054a34074b6b1a66db4a0523aff072421b9257a", "mtime": 1752463757, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\rest_base.py", "plugin_data": null, "size": 17851, "suppressed": ["google.longrunning", "google.protobuf"], "version_id": "1.17.1"}