{"data_mtime": 1754415046, "dep_lines": [5, 6, 7, 8, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 99, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.batch", "openai.types.image", "openai.types.model", "openai.types.shared", "openai.types.upload", "openai.types.embedding", "openai.types.chat_model", "openai.types.completion", "openai.types.moderation", "openai.types.audio_model", "openai.types.batch_error", "openai.types.file_object", "openai.types.image_model", "openai.types.file_content", "openai.types.file_deleted", "openai.types.file_purpose", "openai.types.vector_store", "openai.types.model_deleted", "openai.types.embedding_model", "openai.types.images_response", "openai.types.completion_usage", "openai.types.eval_list_params", "openai.types.file_list_params", "openai.types.moderation_model", "openai.types.batch_list_params", "openai.types.completion_choice", "openai.types.image_edit_params", "openai.types.eval_create_params", "openai.types.eval_list_response", "openai.types.eval_update_params", "openai.types.file_create_params", "openai.types.batch_create_params", "openai.types.batch_request_counts", "openai.types.eval_create_response", "openai.types.eval_delete_response", "openai.types.eval_update_response", "openai.types.upload_create_params", "openai.types.vector_store_deleted", "openai.types.audio_response_format", "openai.types.container_list_params", "openai.types.image_generate_params", "openai.types.eval_retrieve_response", "openai.types.file_chunking_strategy", "openai.types.image_gen_stream_event", "openai.types.upload_complete_params", "openai.types.container_create_params", "openai.types.container_list_response", "openai.types.embedding_create_params", "openai.types.image_edit_stream_event", "openai.types.completion_create_params", "openai.types.moderation_create_params", "openai.types.vector_store_list_params", "openai.types.container_create_response", "openai.types.create_embedding_response", "openai.types.image_gen_completed_event", "openai.types.image_edit_completed_event", "openai.types.moderation_create_response", "openai.types.vector_store_create_params", "openai.types.vector_store_search_params", "openai.types.vector_store_update_params", "openai.types.container_retrieve_response", "openai.types.moderation_text_input_param", "openai.types.file_chunking_strategy_param", "openai.types.vector_store_search_response", "openai.types.websocket_connection_options", "openai.types.image_create_variation_params", "openai.types.image_gen_partial_image_event", "openai.types.static_file_chunking_strategy", "openai.types.eval_custom_data_source_config", "openai.types.image_edit_partial_image_event", "openai.types.moderation_image_url_input_param", "openai.types.auto_file_chunking_strategy_param", "openai.types.moderation_multi_modal_input_param", "openai.types.other_file_chunking_strategy_object", "openai.types.static_file_chunking_strategy_param", "openai.types.static_file_chunking_strategy_object", "openai.types.eval_stored_completions_data_source_config", "openai.types.static_file_chunking_strategy_object_param", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "63589503dd999f04a3b3183b59468ea49927aa69", "id": "openai.types", "ignore_all": true, "interface_hash": "5152cd33ccfe97c2f11f52b0378de9cd999a9112", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\__init__.py", "plugin_data": null, "size": 6703, "suppressed": [], "version_id": "1.17.1"}