{".class": "MypyFile", "_fullname": "openai.types.fine_tuning", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DpoHyperparameters": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.dpo_hyperparameters.DpoHyperparameters", "kind": "Gdef"}, "DpoHyperparametersParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.dpo_hyperparameters_param.DpoHyperparametersParam", "kind": "Gdef"}, "DpoMethod": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.dpo_method.DpoMethod", "kind": "Gdef"}, "DpoMethodParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.dpo_method_param.DpoMethodParam", "kind": "Gdef"}, "FineTuningJob": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.fine_tuning_job.FineTuningJob", "kind": "Gdef"}, "FineTuningJobEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.fine_tuning_job_event.FineTuningJobEvent", "kind": "Gdef"}, "FineTuningJobIntegration": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.fine_tuning_job_integration.FineTuningJobIntegration", "kind": "Gdef"}, "FineTuningJobWandbIntegration": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.fine_tuning_job_wandb_integration.FineTuningJobWandbIntegration", "kind": "Gdef"}, "FineTuningJobWandbIntegrationObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.fine_tuning_job_wandb_integration_object.FineTuningJobWandbIntegrationObject", "kind": "Gdef"}, "JobCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.job_create_params.JobCreateParams", "kind": "Gdef"}, "JobListEventsParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.job_list_events_params.JobListEventsParams", "kind": "Gdef"}, "JobListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.job_list_params.JobListParams", "kind": "Gdef"}, "ReinforcementHyperparameters": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.reinforcement_hyperparameters.ReinforcementHyperparameters", "kind": "Gdef"}, "ReinforcementHyperparametersParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.reinforcement_hyperparameters_param.ReinforcementHyperparametersParam", "kind": "Gdef"}, "ReinforcementMethod": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.reinforcement_method.ReinforcementMethod", "kind": "Gdef"}, "ReinforcementMethodParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.reinforcement_method_param.ReinforcementMethodParam", "kind": "Gdef"}, "SupervisedHyperparameters": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.supervised_hyperparameters.SupervisedHyperparameters", "kind": "Gdef"}, "SupervisedHyperparametersParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.supervised_hyperparameters_param.SupervisedHyperparametersParam", "kind": "Gdef"}, "SupervisedMethod": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.supervised_method.SupervisedMethod", "kind": "Gdef"}, "SupervisedMethodParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.supervised_method_param.SupervisedMethodParam", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\fine_tuning\\__init__.py"}