{"data_mtime": 1754415047, "dep_lines": [15, 29, 9, 14, 15, 27, 10, 11, 12, 13, 26, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.chat.completion_create_params", "openai.types.chat.chat_completion_message_tool_call", "openai.lib._tools", "openai.lib._pydantic", "openai.types.chat", "openai.types.shared_params", "openai._types", "openai._utils", "openai._compat", "openai._models", "openai._exceptions", "__future__", "json", "typing", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "abc", "openai.types", "openai.types.chat.chat_completion", "openai.types.chat.chat_completion_message", "openai.types.chat.chat_completion_tool_param", "openai.types.chat.parsed_chat_completion", "openai.types.chat.parsed_function_tool_call", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "8f018a88f7a830b0800643c75b315a88f38d3f80", "id": "openai.lib._parsing._completions", "ignore_all": true, "interface_hash": "936f87a3fece334516f5df4684fa7a93b5b5f06d", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\_parsing\\_completions.py", "plugin_data": null, "size": 9150, "suppressed": [], "version_id": "1.17.1"}