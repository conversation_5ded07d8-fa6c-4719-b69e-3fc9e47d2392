{"data_mtime": 1754415052, "dep_lines": [25, 26, 20, 22, 23, 24, 25, 27, 28, 30, 20, 22, 27, 15, 17, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 10, 10, 5, 20, 20, 20, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["google.generativeai.types.model_types", "google.generativeai.types.helper_types", "google.ai.generativelanguage", "google.generativeai.protos", "google.generativeai.operations", "google.generativeai.client", "google.generativeai.types", "google.api_core.operation", "google.api_core.protobuf_helpers", "google.generativeai.utils", "google.ai", "google.generativeai", "google.api_core", "__future__", "typing", "google", "builtins", "_frozen_importlib", "abc", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.model_service", "google.ai.generativelanguage_v1beta.services.model_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.model", "google.ai.generativelanguage_v1beta.types.tuned_model", "google.api_core.future", "google.api_core.future.base", "google.api_core.future.polling", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.api_core.timeout", "os", "pathlib"], "hash": "b02a9ee435bb1313d8d26f24c9e31c8fa7a0bb92", "id": "google.generativeai.models", "ignore_all": true, "interface_hash": "54ee07e5c348d8ea88bd08e2392c69095f590dbb", "mtime": 1754404370, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\models.py", "plugin_data": null, "size": 15915, "suppressed": ["google.protobuf"], "version_id": "1.17.1"}