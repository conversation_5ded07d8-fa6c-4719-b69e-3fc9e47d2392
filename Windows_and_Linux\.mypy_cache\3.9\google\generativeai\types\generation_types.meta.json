{"data_mtime": 1754415052, "dep_lines": [34, 30, 32, 33, 34, 35, 19, 30, 32, 15, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29, 29], "dep_prios": [10, 10, 10, 10, 20, 5, 5, 20, 20, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["google.generativeai.types.content_types", "google.api_core.exceptions", "google.generativeai.protos", "google.generativeai.string_utils", "google.generativeai.types", "google.generativeai.responder", "collections.abc", "google.api_core", "google.generativeai", "__future__", "collections", "contextlib", "dataclasses", "itertools", "json", "sys", "textwrap", "typing", "typing_extensions", "types", "google", "builtins", "_frozen_importlib", "_typeshed", "abc", "google.ai", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.citation", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.safety"], "hash": "c977b0969452cf48e3905e98712a36240cd11dce", "id": "google.generativeai.types.generation_types", "ignore_all": true, "interface_hash": "15f7aefe7bb60b691472c8e66f123103752fbed5", "mtime": 1754404370, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\types\\generation_types.py", "plugin_data": null, "size": 26055, "suppressed": ["google.protobuf.json_format", "google.protobuf"], "version_id": "1.17.1"}