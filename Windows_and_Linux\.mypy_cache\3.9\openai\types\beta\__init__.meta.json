{"data_mtime": 1754415047, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.beta.thread", "openai.types.beta.assistant", "openai.types.beta.function_tool", "openai.types.beta.assistant_tool", "openai.types.beta.thread_deleted", "openai.types.beta.file_search_tool", "openai.types.beta.assistant_deleted", "openai.types.beta.function_tool_param", "openai.types.beta.assistant_tool_param", "openai.types.beta.thread_create_params", "openai.types.beta.thread_update_params", "openai.types.beta.assistant_list_params", "openai.types.beta.assistant_tool_choice", "openai.types.beta.code_interpreter_tool", "openai.types.beta.assistant_stream_event", "openai.types.beta.file_search_tool_param", "openai.types.beta.assistant_create_params", "openai.types.beta.assistant_update_params", "openai.types.beta.assistant_tool_choice_param", "openai.types.beta.code_interpreter_tool_param", "openai.types.beta.assistant_tool_choice_option", "openai.types.beta.thread_create_and_run_params", "openai.types.beta.assistant_tool_choice_function", "openai.types.beta.assistant_response_format_option", "openai.types.beta.assistant_tool_choice_option_param", "openai.types.beta.assistant_tool_choice_function_param", "openai.types.beta.assistant_response_format_option_param", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b4c77b7ffbeb4633d8dce890e6f941f19ae75e87", "id": "openai.types.beta", "ignore_all": true, "interface_hash": "c1d8927948d0bc497fb8aff3b9559b385117f202", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\__init__.py", "plugin_data": null, "size": 2328, "suppressed": [], "version_id": "1.17.1"}