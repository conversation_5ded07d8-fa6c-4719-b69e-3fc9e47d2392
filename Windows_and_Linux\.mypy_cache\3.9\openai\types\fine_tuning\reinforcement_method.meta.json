{"data_mtime": 1754415046, "dep_lines": [7, 8, 9, 10, 11, 12, 6, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.graders.multi_grader", "openai.types.graders.python_grader", "openai.types.graders.score_model_grader", "openai.types.graders.string_check_grader", "openai.types.fine_tuning.reinforcement_hyperparameters", "openai.types.graders.text_similarity_grader", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "openai.types.graders", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "3abb60579e491dfaa59251f20c90cce2df61f5db", "id": "openai.types.fine_tuning.reinforcement_method", "ignore_all": true, "interface_hash": "bb899fd0b6cb63b72d7ae50ed069f17f8ab8e873", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method.py", "plugin_data": null, "size": 958, "suppressed": [], "version_id": "1.17.1"}