{"data_mtime": 1754415047, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 20, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.evals.eval_api_error", "openai.types.evals.run_list_params", "openai.types.evals.run_create_params", "openai.types.evals.run_list_response", "openai.types.evals.run_cancel_response", "openai.types.evals.run_create_response", "openai.types.evals.run_delete_response", "openai.types.evals.run_retrieve_response", "openai.types.evals.create_eval_jsonl_run_data_source", "openai.types.evals.create_eval_completions_run_data_source", "openai.types.evals.create_eval_jsonl_run_data_source_param", "openai.types.evals.create_eval_completions_run_data_source_param", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "055180f48fb58d770dbe8e53a86b85e33e749cbc", "id": "openai.types.evals", "ignore_all": true, "interface_hash": "75a2916dc17e5b19ecaf2fe612469065cad79626", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\evals\\__init__.py", "plugin_data": null, "size": 1193, "suppressed": [], "version_id": "1.17.1"}