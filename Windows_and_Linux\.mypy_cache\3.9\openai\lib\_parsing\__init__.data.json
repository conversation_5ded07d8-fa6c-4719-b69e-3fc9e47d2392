{".class": "MypyFile", "_fullname": "openai.lib._parsing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ResponseFormatT": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.ResponseFormatT", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "get_input_tool_by_name": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.get_input_tool_by_name", "kind": "Gdef"}, "has_parseable_input": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.has_parseable_input", "kind": "Gdef"}, "maybe_parse_content": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.maybe_parse_content", "kind": "Gdef"}, "parse_chat_completion": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.parse_chat_completion", "kind": "Gdef"}, "parse_function_tool_arguments": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.parse_function_tool_arguments", "kind": "Gdef"}, "solve_response_format_t": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.solve_response_format_t", "kind": "Gdef"}, "type_to_response_format_param": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.type_to_response_format_param", "kind": "Gdef"}, "validate_input_tools": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.validate_input_tools", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\_parsing\\__init__.py"}