{".class": "MypyFile", "_fullname": "google.generativeai.types.content_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BlobDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.BlobDict", "name": "BlobDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.BlobDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.BlobDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mime_type", "builtins.str"], ["data", "builtins.bytes"]], "readonly_keys": [], "required_keys": ["data", "mime_type"]}}}, "BlobType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.generativeai.types.content_types.BlobType", "line": 190, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.BlobDict"}, "PIL.Image.Image", {".class": "AnyType", "missing_import_name": "google.generativeai.types.content_types.IPython", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CallableFunctionDeclaration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.generativeai.types.content_types.FunctionDeclaration"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.CallableFunctionDeclaration", "name": "CallableFunctionDeclaration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.CallableFunctionDeclaration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.CallableFunctionDeclaration", "google.generativeai.types.content_types.FunctionDeclaration", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.CallableFunctionDeclaration.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fc"], "arg_types": ["google.generativeai.types.content_types.CallableFunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionCall"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of CallableFunctionDeclaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 3], "arg_names": ["self", "name", "description", "parameters", "function"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.CallableFunctionDeclaration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 3], "arg_names": ["self", "name", "description", "parameters", "function"], "arg_types": ["google.generativeai.types.content_types.CallableFunctionDeclaration", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CallableFunctionDeclaration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.CallableFunctionDeclaration.function", "name": "function", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.content_types.CallableFunctionDeclaration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.content_types.CallableFunctionDeclaration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.ContentDict", "name": "ContentDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.ContentDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.ContentDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["parts", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.PartType"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["role", "builtins.str"]], "readonly_keys": [], "required_keys": ["parts", "role"]}}}, "ContentType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ContentType", "line": 278, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Content", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentDict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.PartType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.PartType"}], "uses_pep604_syntax": false}}}, "ContentsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ContentsType", "line": 316, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.StrictContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "DynamicRetrievalConfig": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.DynamicRetrievalConfig", "line": 687, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.DynamicRetrievalConfig", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.DynamicRetrievalConfigDict"}], "uses_pep604_syntax": false}}}, "DynamicRetrievalConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.DynamicRetrievalConfigDict", "name": "DynamicRetrievalConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.DynamicRetrievalConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.DynamicRetrievalConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mode", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "protos.DynamicRetrievalConfig.mode"}], ["dynamic_threshold", "builtins.float"]], "readonly_keys": [], "required_keys": ["dynamic_threshold", "mode"]}}}, "FunctionCallingConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.FunctionCallingConfigDict", "name": "FunctionCallingConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.FunctionCallingConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.FunctionCallingConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mode", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionCallingModeType"}], ["allowed_function_names", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}]], "readonly_keys": [], "required_keys": ["allowed_function_names", "mode"]}}}, "FunctionCallingConfigType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.FunctionCallingConfigType", "line": 955, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionCallingModeType"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionCallingConfigDict"}, "google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig"], "uses_pep604_syntax": false}}}, "FunctionCallingMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.FunctionCallingMode", "line": 920, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig.Mode"}}, "FunctionCallingModeType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.FunctionCallingModeType", "line": 941, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig.Mode", "builtins.str", "builtins.int"], "uses_pep604_syntax": false}}}, "FunctionDeclaration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.FunctionDeclaration", "name": "FunctionDeclaration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.FunctionDeclaration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.FunctionDeclaration", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "name", "description", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "name", "description", "parameters"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDeclaration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_proto": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration._proto", "name": "_proto", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "description of FunctionDeclaration", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.description", "name": "description", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "description of FunctionDeclaration", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["function", "descriptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.from_function", "name": "from_function", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["function", "descriptions"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_function of FunctionDeclaration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.from_function", "name": "from_function", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["function", "descriptions"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_function of FunctionDeclaration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.from_proto", "name": "from_proto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "proto"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.content_types.FunctionDeclaration"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_proto of FunctionDeclaration", "ret_type": "google.generativeai.types.content_types.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.from_proto", "name": "from_proto", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "proto"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.content_types.FunctionDeclaration"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_proto of FunctionDeclaration", "ret_type": "google.generativeai.types.content_types.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of FunctionDeclaration", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of FunctionDeclaration", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parameters of FunctionDeclaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.Schema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.parameters", "name": "parameters", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parameters of FunctionDeclaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.Schema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionDeclaration.to_proto", "name": "to_proto", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.FunctionDeclaration"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_proto of FunctionDeclaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.content_types.FunctionDeclaration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.content_types.FunctionDeclaration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionDeclarationType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.FunctionDeclarationType", "line": 647, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "FunctionLibrary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.FunctionLibrary", "name": "FunctionLibrary", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.FunctionLibrary", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.FunctionLibrary", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionLibrary.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fc"], "arg_types": ["google.generativeai.types.content_types.FunctionLibrary", "google.ai.generativelanguage_v1beta.types.content.FunctionCall"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FunctionLibrary", "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Part", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionLibrary.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.generativeai.types.content_types.FunctionLibrary", {".class": "UnionType", "items": ["builtins.str", "google.ai.generativelanguage_v1beta.types.content.FunctionCall"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of FunctionLibrary", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.FunctionLibrary.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tools"], "arg_types": ["google.generativeai.types.content_types.FunctionLibrary", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FunctionLibrary", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionLibrary._index", "name": "_index", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.FunctionLibrary._tools", "name": "_tools", "setter_type": null, "type": {".class": "Instance", "args": ["google.generativeai.types.content_types.Tool"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.FunctionLibrary.to_proto", "name": "to_proto", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.content_types.FunctionLibrary.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.content_types.FunctionLibrary", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionLibraryType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.FunctionLibraryType", "line": 908, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionLibrary", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolsType"}], "uses_pep604_syntax": false}}}, "GoogleSearchRetrievalDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.GoogleSearchRetrievalDict", "name": "GoogleSearchRetrievalDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.GoogleSearchRetrievalDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.GoogleSearchRetrievalDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["dynamic_retrieval_config", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.DynamicRetrievalConfig"}]], "readonly_keys": [], "required_keys": ["dynamic_retrieval_config"]}}}, "GoogleSearchRetrievalType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.GoogleSearchRetrievalType", "line": 694, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.GoogleSearchRetrieval", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.GoogleSearchRetrievalDict"}], "uses_pep604_syntax": false}}}, "IMAGE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.content_types.IMAGE_TYPES", "name": "IMAGE_TYPES", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "Image", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": "google.generativeai.types.content_types.IPython", "source_any": {".class": "AnyType", "missing_import_name": "google.generativeai.types.content_types.IPython", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "IPython": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.IPython", "name": "IPython", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.generativeai.types.content_types.IPython", "source_any": null, "type_of_any": 3}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Mode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.Mode", "line": 75, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.content.DynamicRetrievalConfig.Mode"}}, "ModeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ModeOptions", "line": 77, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", "builtins.str", "google.ai.generativelanguage_v1beta.types.content.DynamicRetrievalConfig.Mode"], "uses_pep604_syntax": false}}}, "PIL": {".class": "SymbolTableNode", "cross_ref": "PIL", "kind": "Gdef", "module_public": false}, "PartDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.PartDict", "name": "PartDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.PartDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.PartDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["text", "builtins.str"], ["inline_data", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.BlobType"}]], "readonly_keys": [], "required_keys": ["inline_data", "text"]}}}, "PartType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.PartType", "line": 224, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Part", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.PartDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.BlobType"}, "builtins.str", "google.ai.generativelanguage_v1beta.types.content.FunctionCall", "google.ai.generativelanguage_v1beta.types.content.FunctionResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.file_types.FileDataType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StrictContentType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.StrictContentType", "line": 281, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Content", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentDict"}], "uses_pep604_syntax": false}}}, "StructType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.StructType", "line": 619, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ValueType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Tool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.Tool", "name": "Tool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.Tool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.Tool", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.Tool.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fc"], "arg_types": ["google.generativeai.types.content_types.Tool", "google.ai.generativelanguage_v1beta.types.content.FunctionCall"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of Tool", "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.FunctionResponse", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.Tool.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.generativeai.types.content_types.Tool", {".class": "UnionType", "items": ["builtins.str", "google.ai.generativelanguage_v1beta.types.content.FunctionCall"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of Tool", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "function_declarations", "google_search_retrieval", "code_execution"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.content_types.Tool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "function_declarations", "google_search_retrieval", "code_execution"], "arg_types": ["google.generativeai.types.content_types.Tool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionDeclarationType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.GoogleSearchRetrievalType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.CodeExecution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Tool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_function_declarations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.Tool._function_declarations", "name": "_function_declarations", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_google_search_retrieval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.Tool._google_search_retrieval", "name": "_google_search_retrieval", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.Tool._index", "name": "_index", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_proto": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.content_types.Tool._proto", "name": "_proto", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.content.Tool"}}, "code_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.Tool.code_execution", "name": "code_execution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.Tool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "code_execution of Tool", "ret_type": "google.ai.generativelanguage_v1beta.types.content.CodeExecution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.Tool.code_execution", "name": "code_execution", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.Tool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "code_execution of Tool", "ret_type": "google.ai.generativelanguage_v1beta.types.content.CodeExecution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "function_declarations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.Tool.function_declarations", "name": "function_declarations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.Tool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "function_declarations of Tool", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.Tool.function_declarations", "name": "function_declarations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.Tool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "function_declarations of Tool", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "google_search_retrieval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.content_types.Tool.google_search_retrieval", "name": "google_search_retrieval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.Tool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "google_search_retrieval of <PERSON>l", "ret_type": "google.ai.generativelanguage_v1beta.types.content.GoogleSearchRetrieval", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.content_types.Tool.google_search_retrieval", "name": "google_search_retrieval", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.content_types.Tool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "google_search_retrieval of <PERSON>l", "ret_type": "google.ai.generativelanguage_v1beta.types.content.GoogleSearchRetrieval", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.Tool.to_proto", "name": "to_proto", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.content_types.Tool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.content_types.Tool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.ToolConfigDict", "name": "ToolConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.ToolConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.ToolConfigDict", "builtins.object"], "names": {".class": "SymbolTable", "function_calling_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.generativeai.types.content_types.ToolConfigDict.function_calling_config", "name": "function_calling_config", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionCallingConfigType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.content_types.ToolConfigDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.content_types.ToolConfigDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolConfigType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ToolConfigType", "line": 983, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.generativeai.types.content_types.ToolConfigDict", "google.ai.generativelanguage_v1beta.types.content.ToolConfig"], "uses_pep604_syntax": false}}}, "ToolDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.content_types.ToolDict", "name": "ToolDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.ToolDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.content_types", "mro": ["google.generativeai.types.content_types.ToolDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["function_declarations", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionDeclarationType"}], "extra_attrs": null, "type_ref": "builtins.list"}]], "readonly_keys": [], "required_keys": ["function_declarations"]}}}, "ToolType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ToolType", "line": 790, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "google.generativeai.types.content_types.Tool", "google.ai.generativelanguage_v1beta.types.content.Tool", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolDict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionDeclarationType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionDeclarationType"}], "uses_pep604_syntax": false}}}, "ToolsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ToolsType", "line": 888, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolType"}], "uses_pep604_syntax": false}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "ValueType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.content_types.ValueType", "line": 620, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.float", "builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.StructType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ValueType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_FUNCTION_CALLING_MODE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.content_types._FUNCTION_CALLING_MODE", "name": "_FUNCTION_CALLING_MODE", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_MODE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.content_types._MODE", "name": "_MODE", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ModeOptions"}, "google.ai.generativelanguage_v1beta.types.content.DynamicRetrievalConfig.Mode"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.content_types.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.content_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.content_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.content_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.content_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.content_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.content_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_build_schema": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fname", "fields_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._build_schema", "name": "_build_schema", "type": null}}, "_convert_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._convert_dict", "name": "_convert_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_dict", "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Content", "google.ai.generativelanguage_v1beta.types.content.Part", "google.ai.generativelanguage_v1beta.types.content.Blob"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_encode_fd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._encode_fd", "name": "_encode_fd", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fd"], "arg_types": [{".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_encode_fd", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_function_declaration": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._make_function_declaration", "name": "_make_function_declaration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fun"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionDeclarationType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_function_declaration", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionDeclaration", "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_google_search_retrieval": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gsr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._make_google_search_retrieval", "name": "_make_google_search_retrieval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gsr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.GoogleSearchRetrievalType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_google_search_retrieval", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_tool": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._make_tool", "name": "_make_tool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_tool", "ret_type": "google.generativeai.types.content_types.Tool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_tools": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._make_tools", "name": "_make_tools", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tools"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolsType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_tools", "ret_type": {".class": "Instance", "args": ["google.generativeai.types.content_types.Tool"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pil_to_blob": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._pil_to_blob", "name": "_pil_to_blob", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["image"], "arg_types": ["PIL.Image.Image"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_pil_to_blob", "ret_type": "google.ai.generativelanguage_v1beta.types.content.Blob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rename_schema_fields": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._rename_schema_fields", "name": "_rename_schema_fields", "type": null}}, "_schema_for_class": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._schema_for_class", "name": "_schema_for_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "TypedDict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_schema_for_class", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_schema_for_function": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["f", "descriptions", "required"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types._schema_for_function", "name": "_schema_for_function", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["f", "descriptions", "required"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_schema_for_function", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_object_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.add_object_type", "name": "add_object_type", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "convert_to_nullable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.convert_to_nullable", "name": "convert_to_nullable", "type": null}}, "file_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.file_types", "kind": "Gdef", "module_public": false}, "image_to_blob": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.image_to_blob", "name": "image_to_blob", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["image"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "image_to_blob", "ret_type": "google.ai.generativelanguage_v1beta.types.content.Blob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "is_blob_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.is_blob_dict", "name": "is_blob_dict", "type": null}}, "is_content_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.is_content_dict", "name": "is_content_dict", "type": null}}, "is_part_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.is_part_dict", "name": "is_part_dict", "type": null}}, "mimetypes": {".class": "SymbolTableNode", "cross_ref": "mimetypes", "kind": "Gdef", "module_public": false}, "pathlib": {".class": "SymbolTableNode", "cross_ref": "pathlib", "kind": "Gdef", "module_public": false}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef", "module_public": false}, "pydantic": {".class": "SymbolTableNode", "cross_ref": "pydantic", "kind": "Gdef", "module_public": false}, "strict_to_content": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.strict_to_content", "name": "strict_to_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["content"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.StrictContentType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "strict_to_content", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strip_additional_properties": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.strip_additional_properties", "name": "strip_additional_properties", "type": null}}, "strip_titles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.strip_titles", "name": "strip_titles", "type": null}}, "to_blob": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["blob"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_blob", "name": "to_blob", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["blob"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.BlobType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_blob", "ret_type": "google.ai.generativelanguage_v1beta.types.content.Blob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_content": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_content", "name": "to_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["content"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_content", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_contents": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_contents", "name": "to_contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["contents"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentsType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_contents", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_function_calling_config": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_function_calling_config", "name": "to_function_calling_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionCallingConfigType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_function_calling_config", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_function_calling_mode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_function_calling_mode", "name": "to_function_calling_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionCallingModeType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_function_calling_mode", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig.Mode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_function_library": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lib"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_function_library", "name": "to_function_library", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lib"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_function_library", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionLibrary", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_mode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_mode", "name": "to_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ModeOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_mode", "ret_type": "google.ai.generativelanguage_v1beta.types.content.DynamicRetrievalConfig.Mode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_part": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["part"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_part", "name": "to_part", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["part"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.PartType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_part", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_tool_config": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.to_tool_config", "name": "to_tool_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_tool_config", "ret_type": "google.ai.generativelanguage_v1beta.types.content.ToolConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "unpack_defs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["schema", "defs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.content_types.unpack_defs", "name": "unpack_defs", "type": null}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\types\\content_types.py"}