{".class": "MypyFile", "_fullname": "google.generativeai.generative_models", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ChatSession": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.generative_models.ChatSession", "name": "ChatSession", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.generative_models.ChatSession", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.generative_models", "mro": ["google.generativeai.generative_models.ChatSession", "builtins.object"], "names": {".class": "SymbolTable", "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.generative_models.ChatSession.__copy__", "name": "__copy__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "history", "enable_automatic_function_calling"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "history", "enable_automatic_function_calling"], "arg_types": ["google.generativeai.generative_models.ChatSession", "google.generativeai.generative_models.GenerativeModel", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.StrictContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ChatSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ChatSession", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "response", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.generative_models.ChatSession._check_response", "name": "_check_response", "type": null}}, "_get_function_calls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession._get_function_calls", "name": "_get_function_calls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_function_calls of ChatSession", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.FunctionCall"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_afc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "response", "history", "generation_config", "safety_settings", "stream", "tools_lib", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession._handle_afc", "name": "_handle_afc", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "response", "history", "generation_config", "safety_settings", "stream", "tools_lib", "request_options"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_afc of ChatSession", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}, "google.ai.generativelanguage_v1beta.types.content.Content", "google.generativeai.types.generation_types.BaseGenerateContentResponse"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_afc_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "response", "history", "generation_config", "safety_settings", "stream", "tools_lib", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession._handle_afc_async", "name": "_handle_afc_async", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "response", "history", "generation_config", "safety_settings", "stream", "tools_lib", "request_options"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_afc_async of ChatSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}, "google.ai.generativelanguage_v1beta.types.content.Content", "google.generativeai.types.generation_types.BaseGenerateContentResponse"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession._history", "name": "_history", "setter_type": null, "type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_last_received": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession._last_received", "name": "_last_received", "setter_type": null, "type": {".class": "UnionType", "items": ["google.generativeai.types.generation_types.BaseGenerateContentResponse", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession._last_sent", "name": "_last_sent", "setter_type": null, "type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Content", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "enable_automatic_function_calling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession.enable_automatic_function_calling", "name": "enable_automatic_function_calling", "setter_type": null, "type": "builtins.bool"}}, "history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "google.generativeai.generative_models.ChatSession.history", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.history", "name": "history", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "history of ChatSession", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession.history", "name": "history", "setter_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "history"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "history of ChatSession", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "history of ChatSession", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "history"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "google.generativeai.generative_models.ChatSession.history", "name": "history", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "", "name": "history", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "history"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "history of ChatSession", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": 1, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "history of ChatSession", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.content.Content"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "last": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.last", "name": "last", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "last of ChatSession", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.generation_types.BaseGenerateContentResponse", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession.last", "name": "last", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "last of ChatSession", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.generation_types.BaseGenerateContentResponse", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.ChatSession.model", "name": "model", "setter_type": null, "type": "google.generativeai.generative_models.GenerativeModel"}}, "rewind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.rewind", "name": "rewind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.ChatSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rewind of ChatSession", "ret_type": {".class": "TupleType", "implicit": false, "items": ["google.ai.generativelanguage_v1beta.types.content.Content", "google.ai.generativelanguage_v1beta.types.content.Content"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "content", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.send_message", "name": "send_message", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "content", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_message of ChatSession", "ret_type": "google.generativeai.types.generation_types.GenerateContentResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_message_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "content", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.generative_models.ChatSession.send_message_async", "name": "send_message_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "content", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "arg_types": ["google.generativeai.generative_models.ChatSession", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_message_async of ChatSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.generation_types.AsyncGenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.generative_models.ChatSession.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.generative_models.ChatSession", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GenerativeModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.generative_models.GenerativeModel", "name": "GenerativeModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.generative_models.GenerativeModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.generative_models", "mro": ["google.generativeai.generative_models.GenerativeModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model_name", "safety_settings", "generation_config", "tools", "tool_config", "system_instruction"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model_name", "safety_settings", "generation_config", "tools", "tool_config", "system_instruction"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of GenerativeModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.generative_models.GenerativeModel.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.generativeai.generative_models.GenerativeModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.generative_models.GenerativeModel.__str__", "name": "__str__", "type": null}}, "_async_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._async_client", "name": "_async_client", "setter_type": null, "type": {".class": "NoneType"}}}, "_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._client", "name": "_client", "setter_type": null, "type": {".class": "NoneType"}}}, "_generation_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._generation_config", "name": "_generation_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_tools_lib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel._get_tools_lib", "name": "_get_tools_lib", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tools"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_tools_lib of GenerativeModel", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionLibrary", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_model_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._model_name", "name": "_model_name", "setter_type": null, "type": "builtins.str"}}, "_prepare_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 3, 3], "arg_names": ["self", "contents", "generation_config", "safety_settings", "tools", "tool_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel._prepare_request", "name": "_prepare_request", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 3, 3], "arg_names": ["self", "contents", "generation_config", "safety_settings", "tools", "tool_config"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_request of GenerativeModel", "ret_type": "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safety_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._safety_settings", "name": "_safety_settings", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmCategoryOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_system_instruction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._system_instruction", "name": "_system_instruction", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_tool_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._tool_config", "name": "_tool_config", "setter_type": null, "type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.ToolConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel._tools", "name": "_tools", "setter_type": null, "type": {".class": "UnionType", "items": ["google.generativeai.types.content_types.FunctionLibrary", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.cached_content", "name": "cached_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.GenerativeModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cached_content of GenerativeModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel.cached_content", "name": "cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.GenerativeModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cached_content of GenerativeModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "count_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "tools", "tool_config", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.count_tokens", "name": "count_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "tools", "tool_config", "request_options"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_tokens of GenerativeModel", "ret_type": "google.ai.generativelanguage_v1beta.types.generative_service.CountTokensResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count_tokens_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "tools", "tool_config", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.count_tokens_async", "name": "count_tokens_async", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "tools", "tool_config", "request_options"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_tokens_async of GenerativeModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.ai.generativelanguage_v1beta.types.generative_service.CountTokensResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "name": "from_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, {".class": "UnionType", "items": ["builtins.str", "google.generativeai.caching.CachedContent"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "name": "from_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, {".class": "UnionType", "items": ["builtins.str", "google.generativeai.caching.CachedContent"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "name": "from_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "name": "from_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "name": "from_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, "google.generativeai.caching.CachedContent", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel.from_cached_content", "name": "from_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, "google.generativeai.caching.CachedContent", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "cached_content", "generation_config", "safety_settings"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.generative_models.GenerativeModel"}, "google.generativeai.caching.CachedContent", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_cached_content of GenerativeModel", "ret_type": "google.generativeai.generative_models.GenerativeModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "generate_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.generate_content", "name": "generate_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_content of GenerativeModel", "ret_type": "google.generativeai.types.generation_types.GenerateContentResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_content_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.generate_content_async", "name": "generate_content_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "contents", "generation_config", "safety_settings", "stream", "tools", "tool_config", "request_options"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.generation_types.GenerationConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.FunctionLibraryType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ToolConfigType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_content_async of GenerativeModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.generation_types.AsyncGenerateContentResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.generative_models.GenerativeModel.model_name", "name": "model_name", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.generative_models.GenerativeModel.model_name", "name": "model_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.generative_models.GenerativeModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_name of GenerativeModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "history", "enable_automatic_function_calling"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.generative_models.GenerativeModel.start_chat", "name": "start_chat", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "history", "enable_automatic_function_calling"], "arg_types": ["google.generativeai.generative_models.GenerativeModel", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.StrictContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "start_chat of GenerativeModel", "ret_type": "google.generativeai.generative_models.ChatSession", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.generative_models.GenerativeModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.generative_models.GenerativeModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_MODEL_ROLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.generative_models._MODEL_ROLE", "name": "_MODEL_ROLE", "setter_type": null, "type": "builtins.str"}}, "_USER_ROLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.generative_models._USER_ROLE", "name": "_USER_ROLE", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.generative_models.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.generative_models.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.generative_models.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.generative_models.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.generative_models.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.generative_models.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "caching": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.caching", "kind": "Gdef"}, "client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client", "kind": "Gdef"}, "content_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types", "kind": "Gdef"}, "generation_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types", "kind": "Gdef"}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef"}, "helper_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.helper_types", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef"}, "reprlib": {".class": "SymbolTableNode", "cross_ref": "reprlib", "kind": "Gdef"}, "safety_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\generative_models.py"}