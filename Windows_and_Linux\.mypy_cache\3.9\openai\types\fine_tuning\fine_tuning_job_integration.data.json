{".class": "MypyFile", "_fullname": "openai.types.fine_tuning.fine_tuning_job_integration", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FineTuningJobIntegration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.FineTuningJobIntegration", "line": 5, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "openai.types.fine_tuning.fine_tuning_job_wandb_integration_object.FineTuningJobWandbIntegrationObject"}}, "FineTuningJobWandbIntegrationObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.fine_tuning_job_wandb_integration_object.FineTuningJobWandbIntegrationObject", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.fine_tuning_job_integration.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_integration.py"}