{"data_mtime": 1754414997, "dep_lines": [7, 8, 9, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["subprocess", "sys", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing", "typing_extensions"], "hash": "20298656eaa7a12346b325a38139951d81106f34", "id": "lint", "ignore_all": false, "interface_hash": "862a3c0da716d0bfe7ee1ac57102ed452a5e48b0", "mtime": 1754415154, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": ".\\scripts\\lint.py", "plugin_data": null, "size": 2533, "suppressed": [], "version_id": "1.17.1"}