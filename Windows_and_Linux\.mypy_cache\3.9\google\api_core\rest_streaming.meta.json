{"data_mtime": 1754415333, "dep_lines": [22, 17, 20, 21, 1, 1, 1, 1, 21, 21, 19], "dep_prios": [5, 5, 10, 20, 5, 30, 30, 30, 10, 20, 10], "dependencies": ["google.api_core._rest_streaming_base", "typing", "requests", "google", "builtins", "_frozen_importlib", "abc", "requests.models"], "hash": "8d4e39af11840472b6c562fc843df02467dde6e3", "id": "google.api_core.rest_streaming", "ignore_all": true, "interface_hash": "07fc06e2af92f4c7996e87110f041de581731df5", "mtime": 1752463755, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\api_core\\rest_streaming.py", "plugin_data": null, "size": 2209, "suppressed": ["google.protobuf.message", "google.protobuf", "proto"], "version_id": "1.17.1"}