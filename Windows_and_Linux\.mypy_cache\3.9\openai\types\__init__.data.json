{".class": "MypyFile", "_fullname": "openai.types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllModels": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.all_models.AllModels", "kind": "Gdef"}, "AudioModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio_model.AudioModel", "kind": "Gdef"}, "AudioResponseFormat": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio_response_format.AudioResponseFormat", "kind": "Gdef"}, "AutoFileChunkingStrategyParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.auto_file_chunking_strategy_param.AutoFileChunkingStrategyParam", "kind": "Gdef"}, "Batch": {".class": "SymbolTableNode", "cross_ref": "openai.types.batch.Batch", "kind": "Gdef"}, "BatchCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.batch_create_params.BatchCreateParams", "kind": "Gdef"}, "BatchError": {".class": "SymbolTableNode", "cross_ref": "openai.types.batch_error.BatchError", "kind": "Gdef"}, "BatchListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.batch_list_params.BatchListParams", "kind": "Gdef"}, "BatchRequestCounts": {".class": "SymbolTableNode", "cross_ref": "openai.types.batch_request_counts.BatchRequestCounts", "kind": "Gdef"}, "ChatModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.chat_model.ChatModel", "kind": "Gdef"}, "ComparisonFilter": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.comparison_filter.ComparisonFilter", "kind": "Gdef"}, "Completion": {".class": "SymbolTableNode", "cross_ref": "openai.types.completion.Completion", "kind": "Gdef"}, "CompletionChoice": {".class": "SymbolTableNode", "cross_ref": "openai.types.completion_choice.CompletionChoice", "kind": "Gdef"}, "CompletionCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.completion_create_params.CompletionCreateParams", "kind": "Gdef"}, "CompletionUsage": {".class": "SymbolTableNode", "cross_ref": "openai.types.completion_usage.CompletionUsage", "kind": "Gdef"}, "CompoundFilter": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.compound_filter.CompoundFilter", "kind": "Gdef"}, "ContainerCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.container_create_params.ContainerCreateParams", "kind": "Gdef"}, "ContainerCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.container_create_response.ContainerCreateResponse", "kind": "Gdef"}, "ContainerListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.container_list_params.ContainerListParams", "kind": "Gdef"}, "ContainerListResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.container_list_response.ContainerListResponse", "kind": "Gdef"}, "ContainerRetrieveResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.container_retrieve_response.ContainerRetrieveResponse", "kind": "Gdef"}, "CreateEmbeddingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.create_embedding_response.CreateEmbeddingResponse", "kind": "Gdef"}, "Embedding": {".class": "SymbolTableNode", "cross_ref": "openai.types.embedding.Embedding", "kind": "Gdef"}, "EmbeddingCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.embedding_create_params.EmbeddingCreateParams", "kind": "Gdef"}, "EmbeddingModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.embedding_model.EmbeddingModel", "kind": "Gdef"}, "ErrorObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.error_object.ErrorObject", "kind": "Gdef"}, "EvalCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_create_params.EvalCreateParams", "kind": "Gdef"}, "EvalCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_create_response.EvalCreateResponse", "kind": "Gdef"}, "EvalCustomDataSourceConfig": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_custom_data_source_config.EvalCustomDataSourceConfig", "kind": "Gdef"}, "EvalDeleteResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_delete_response.EvalDeleteResponse", "kind": "Gdef"}, "EvalListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_list_params.EvalListParams", "kind": "Gdef"}, "EvalListResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_list_response.EvalListResponse", "kind": "Gdef"}, "EvalRetrieveResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_retrieve_response.EvalRetrieveResponse", "kind": "Gdef"}, "EvalStoredCompletionsDataSourceConfig": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_stored_completions_data_source_config.EvalStoredCompletionsDataSourceConfig", "kind": "Gdef"}, "EvalUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_update_params.EvalUpdateParams", "kind": "Gdef"}, "EvalUpdateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_update_response.EvalUpdateResponse", "kind": "Gdef"}, "FileChunkingStrategy": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_chunking_strategy.FileChunkingStrategy", "kind": "Gdef"}, "FileChunkingStrategyParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam", "kind": "Gdef"}, "FileContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_content.FileContent", "kind": "Gdef"}, "FileCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_create_params.FileCreateParams", "kind": "Gdef"}, "FileDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_deleted.FileDeleted", "kind": "Gdef"}, "FileListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_list_params.FileListParams", "kind": "Gdef"}, "FileObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_object.FileObject", "kind": "Gdef"}, "FilePurpose": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_purpose.FilePurpose", "kind": "Gdef"}, "FunctionDefinition": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.function_definition.FunctionDefinition", "kind": "Gdef"}, "FunctionParameters": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.function_parameters.FunctionParameters", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "openai.types.image.Image", "kind": "Gdef"}, "ImageCreateVariationParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_create_variation_params.ImageCreateVariationParams", "kind": "Gdef"}, "ImageEditCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_edit_completed_event.ImageEditCompletedEvent", "kind": "Gdef"}, "ImageEditParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_edit_params.ImageEditParams", "kind": "Gdef"}, "ImageEditPartialImageEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_edit_partial_image_event.ImageEditPartialImageEvent", "kind": "Gdef"}, "ImageEditStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_edit_stream_event.ImageEditStreamEvent", "kind": "Gdef"}, "ImageGenCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_gen_completed_event.ImageGenCompletedEvent", "kind": "Gdef"}, "ImageGenPartialImageEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_gen_partial_image_event.ImageGenPartialImageEvent", "kind": "Gdef"}, "ImageGenStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_gen_stream_event.ImageGenStreamEvent", "kind": "Gdef"}, "ImageGenerateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_generate_params.ImageGenerateParams", "kind": "Gdef"}, "ImageModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_model.ImageModel", "kind": "Gdef"}, "ImagesResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.images_response.ImagesResponse", "kind": "Gdef"}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.metadata.Metadata", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "openai.types.model.Model", "kind": "Gdef"}, "ModelDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.model_deleted.ModelDeleted", "kind": "Gdef"}, "Moderation": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation.Moderation", "kind": "Gdef"}, "ModerationCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation_create_params.ModerationCreateParams", "kind": "Gdef"}, "ModerationCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation_create_response.ModerationCreateResponse", "kind": "Gdef"}, "ModerationImageURLInputParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation_image_url_input_param.ModerationImageURLInputParam", "kind": "Gdef"}, "ModerationModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation_model.ModerationModel", "kind": "Gdef"}, "ModerationMultiModalInputParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation_multi_modal_input_param.ModerationMultiModalInputParam", "kind": "Gdef"}, "ModerationTextInputParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.moderation_text_input_param.ModerationTextInputParam", "kind": "Gdef"}, "OtherFileChunkingStrategyObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.other_file_chunking_strategy_object.OtherFileChunkingStrategyObject", "kind": "Gdef"}, "Reasoning": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.reasoning.Reasoning", "kind": "Gdef"}, "ReasoningEffort": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.reasoning_effort.ReasoningEffort", "kind": "Gdef"}, "ResponseFormatJSONObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.response_format_json_object.ResponseFormatJSONObject", "kind": "Gdef"}, "ResponseFormatJSONSchema": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.response_format_json_schema.ResponseFormatJSONSchema", "kind": "Gdef"}, "ResponseFormatText": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.response_format_text.ResponseFormatText", "kind": "Gdef"}, "ResponsesModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.responses_model.ResponsesModel", "kind": "Gdef"}, "StaticFileChunkingStrategy": {".class": "SymbolTableNode", "cross_ref": "openai.types.static_file_chunking_strategy.StaticFileChunkingStrategy", "kind": "Gdef"}, "StaticFileChunkingStrategyObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.static_file_chunking_strategy_object.StaticFileChunkingStrategyObject", "kind": "Gdef"}, "StaticFileChunkingStrategyObjectParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.static_file_chunking_strategy_object_param.StaticFileChunkingStrategyObjectParam", "kind": "Gdef"}, "StaticFileChunkingStrategyParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.static_file_chunking_strategy_param.StaticFileChunkingStrategyParam", "kind": "Gdef"}, "Upload": {".class": "SymbolTableNode", "cross_ref": "openai.types.upload.Upload", "kind": "Gdef"}, "UploadCompleteParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.upload_complete_params.UploadCompleteParams", "kind": "Gdef"}, "UploadCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.upload_create_params.UploadCreateParams", "kind": "Gdef"}, "VectorStore": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store.VectorStore", "kind": "Gdef"}, "VectorStoreCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store_create_params.VectorStoreCreateParams", "kind": "Gdef"}, "VectorStoreDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store_deleted.VectorStoreDeleted", "kind": "Gdef"}, "VectorStoreListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store_list_params.VectorStoreListParams", "kind": "Gdef"}, "VectorStoreSearchParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store_search_params.VectorStoreSearchParams", "kind": "Gdef"}, "VectorStoreSearchResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store_search_response.VectorStoreSearchResponse", "kind": "Gdef"}, "VectorStoreUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_store_update_params.VectorStoreUpdateParams", "kind": "Gdef"}, "WebsocketConnectionOptions": {".class": "SymbolTableNode", "cross_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\__init__.py"}