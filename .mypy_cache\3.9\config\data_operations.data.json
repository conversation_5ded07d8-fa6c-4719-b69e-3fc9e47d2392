{".class": "MypyFile", "_fullname": "config.data_operations", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActionConfig": {".class": "SymbolTableNode", "cross_ref": "config.interfaces.ActionConfig", "kind": "Gdef"}, "DEFAULT_ACTIONS_VALUES": {".class": "SymbolTableNode", "cross_ref": "config.constants.DEFAULT_ACTIONS_VALUES", "kind": "Gdef"}, "DEFAULT_MODELS": {".class": "SymbolTableNode", "cross_ref": "config.constants.DEFAULT_MODELS", "kind": "Gdef"}, "DEFAULT_SYSTEM_VALUES": {".class": "SymbolTableNode", "cross_ref": "config.constants.DEFAULT_SYSTEM_VALUES", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "PROVIDER_DISPLAY_NAMES": {".class": "SymbolTableNode", "cross_ref": "config.constants.PROVIDER_DISPLAY_NAMES", "kind": "Gdef"}, "SystemConfig": {".class": "SymbolTableNode", "cross_ref": "config.interfaces.SystemConfig", "kind": "Gdef"}, "UnifiedSettings": {".class": "SymbolTableNode", "cross_ref": "config.interfaces.UnifiedSettings", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.data_operations.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.data_operations.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.data_operations.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.data_operations.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.data_operations.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.data_operations.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_default_actions_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.create_default_actions_config", "name": "create_default_actions_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_actions_config", "ret_type": {".class": "Instance", "args": ["builtins.str", "config.interfaces.ActionConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_settings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.create_default_settings", "name": "create_default_settings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_settings", "ret_type": "config.interfaces.UnifiedSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_system_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.create_default_system_config", "name": "create_default_system_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_system_config", "ret_type": "config.interfaces.SystemConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_unified_settings_from_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.create_unified_settings_from_data", "name": "create_unified_settings_from_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["user_data"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_unified_settings_from_data", "ret_type": "config.interfaces.UnifiedSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_model_for_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.get_default_model_for_provider", "name": "get_default_model_for_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["provider"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default_model_for_provider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_provider_display_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.get_provider_display_name", "name": "get_provider_display_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["provider"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_provider_display_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_provider_internal_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["display_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.get_provider_internal_name", "name": "get_provider_internal_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["display_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_provider_internal_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_actions_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["user_data", "default_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.merge_actions_data", "name": "merge_actions_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["user_data", "default_values"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "config.interfaces.ActionConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "config.interfaces.ActionConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge_actions_data", "ret_type": {".class": "Instance", "args": ["builtins.str", "config.interfaces.ActionConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_system_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["user_data", "default_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "config.data_operations.merge_system_data", "name": "merge_system_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["user_data", "default_values"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "config.interfaces.SystemConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "config.interfaces.SystemConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge_system_data", "ret_type": {".class": "Instance", "args": ["builtins.str", "config.interfaces.SystemConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\config\\data_operations.py"}