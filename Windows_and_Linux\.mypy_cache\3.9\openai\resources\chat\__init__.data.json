{".class": "MypyFile", "_fullname": "openai.resources.chat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncChat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChat", "kind": "Gdef"}, "AsyncChatWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChatWithRawResponse", "kind": "Gdef"}, "AsyncChatWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChatWithStreamingResponse", "kind": "Gdef"}, "AsyncCompletions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.AsyncCompletions", "kind": "Gdef"}, "AsyncCompletionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.AsyncCompletionsWithRawResponse", "kind": "Gdef"}, "AsyncCompletionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.AsyncCompletionsWithStreamingResponse", "kind": "Gdef"}, "Chat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.Chat", "kind": "Gdef"}, "ChatWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.ChatWithRawResponse", "kind": "Gdef"}, "ChatWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.ChatWithStreamingResponse", "kind": "Gdef"}, "Completions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.Completions", "kind": "Gdef"}, "CompletionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.CompletionsWithRawResponse", "kind": "Gdef"}, "CompletionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.CompletionsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.chat.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\chat\\__init__.py"}