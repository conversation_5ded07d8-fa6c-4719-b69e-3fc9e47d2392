{".class": "MypyFile", "_fullname": "openai.types.beta.threads.message_content_delta", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_public": false}, "ImageFileDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file_delta_block.ImageFileDeltaBlock", "kind": "Gdef", "module_public": false}, "ImageURLDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_url_delta_block.ImageURLDeltaBlock", "kind": "Gdef", "module_public": false}, "MessageContentDelta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.types.beta.threads.message_content_delta.MessageContentDelta", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["openai.types.beta.threads.image_file_delta_block.ImageFileDeltaBlock", "openai.types.beta.threads.text_delta_block.TextDeltaBlock", "openai.types.beta.threads.refusal_delta_block.RefusalDeltaBlock", "openai.types.beta.threads.image_url_delta_block.ImageURLDeltaBlock"], "uses_pep604_syntax": false}}}, "PropertyInfo": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.PropertyInfo", "kind": "Gdef", "module_public": false}, "RefusalDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.refusal_delta_block.RefusalDeltaBlock", "kind": "Gdef", "module_public": false}, "TextDeltaBlock": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text_delta_block.TextDeltaBlock", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.beta.threads.message_content_delta.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.message_content_delta.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.message_content_delta.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.message_content_delta.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.message_content_delta.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.message_content_delta.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.threads.message_content_delta.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_delta.py"}