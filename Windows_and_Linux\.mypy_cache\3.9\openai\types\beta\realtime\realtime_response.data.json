{".class": "MypyFile", "_fullname": "openai.types.beta.realtime.realtime_response", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef", "module_public": false}, "ConversationItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item.ConversationItem", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.metadata.Metadata", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "RealtimeResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse", "name": "RealtimeResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 173, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 176, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 179, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 16, "name": "id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 19, "name": "conversation_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 30, "name": "max_output_tokens", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 36, "name": "metadata", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 46, "name": "modalities", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 54, "name": "object", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "realtime.response"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 57, "name": "output", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.beta.realtime.conversation_item.ConversationItem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 60, "name": "output_audio_format", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 63, "name": "status", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 69, "name": "status_details", "type": {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 72, "name": "temperature", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 75, "name": "usage", "type": {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 83, "name": "voice", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.beta.realtime.realtime_response", "mro": ["openai.types.beta.realtime.realtime_response.RealtimeResponse", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "_request_id", "id", "conversation_id", "max_output_tokens", "metadata", "modalities", "object", "output", "output_audio_format", "status", "status_details", "temperature", "usage", "voice"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "_request_id", "id", "conversation_id", "max_output_tokens", "metadata", "modalities", "object", "output", "output_audio_format", "status", "status_details", "temperature", "usage", "voice"], "arg_types": ["openai.types.beta.realtime.realtime_response.RealtimeResponse", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "realtime.response"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.beta.realtime.conversation_item.ConversationItem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RealtimeResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "conversation_id", "max_output_tokens", "metadata", "modalities", "object", "output", "output_audio_format", "status", "status_details", "temperature", "usage", "voice"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "conversation_id", "max_output_tokens", "metadata", "modalities", "object", "output", "output_audio_format", "status", "status_details", "temperature", "usage", "voice"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "realtime.response"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.beta.realtime.conversation_item.ConversationItem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RealtimeResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "conversation_id", "max_output_tokens", "metadata", "modalities", "object", "output", "output_audio_format", "status", "status_details", "temperature", "usage", "voice"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "realtime.response"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.beta.realtime.conversation_item.ConversationItem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RealtimeResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "conversation_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.conversation_id", "name": "conversation_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.id", "name": "id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_output_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.max_output_tokens", "name": "max_output_tokens", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.metadata", "name": "metadata", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "modalities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.modalities", "name": "modalities", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.object", "name": "object", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "realtime.response"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.output", "name": "output", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.beta.realtime.conversation_item.ConversationItem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "output_audio_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.output_audio_format", "name": "output_audio_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.status", "name": "status", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.status_details", "name": "status_details", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "temperature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.temperature", "name": "temperature", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.usage", "name": "usage", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "voice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.voice", "name": "voice", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.beta.realtime.realtime_response.RealtimeResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.beta.realtime.realtime_response.RealtimeResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeResponseStatus": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", "kind": "Gdef", "module_public": false}, "RealtimeResponseUsage": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_response.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_response.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response.py"}