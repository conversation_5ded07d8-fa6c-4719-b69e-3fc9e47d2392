{"data_mtime": 1754415047, "dep_lines": [16, 24, 24, 31, 32, 15, 24, 25, 26, 27, 28, 29, 30, 3, 5, 6, 7, 8, 9, 10, 12, 13, 15, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 10, 10, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.uploads.parts", "openai.types.upload_create_params", "openai.types.upload_complete_params", "openai.types.upload", "openai.types.file_purpose", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._base_client", "__future__", "io", "os", "logging", "builtins", "typing", "pathlib", "anyio", "httpx", "openai", "_frozen_importlib", "abc", "httpx._config", "openai._models", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "3a106ad808144fac4a3f123f62b191a485333036", "id": "openai.resources.uploads.uploads", "ignore_all": true, "interface_hash": "7a538e582d9cbb783d93e1e3f5d7389909a370db", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\uploads\\uploads.py", "plugin_data": null, "size": 24881, "suppressed": [], "version_id": "1.17.1"}