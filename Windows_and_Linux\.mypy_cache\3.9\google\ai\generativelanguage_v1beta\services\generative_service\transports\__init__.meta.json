{"data_mtime": 1754415051, "dep_lines": [19, 20, 21, 22, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.ai.generativelanguage_v1beta.services.generative_service.transports.base", "google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc", "google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc_asyncio", "google.ai.generativelanguage_v1beta.services.generative_service.transports.rest", "collections", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "google.ai.generativelanguage_v1beta.services.generative_service.transports.rest_base", "google.api_core", "google.api_core.client_info", "google.api_core.gapic_v1", "google.api_core.gapic_v1.client_info", "google.auth", "google.auth._credentials_base", "google.auth.credentials"], "hash": "369dfdb7a9b216f0e36fa88d920790b4a7d2b05e", "id": "google.ai.generativelanguage_v1beta.services.generative_service.transports", "ignore_all": true, "interface_hash": "dccbe24446180c5a3d861879a65846679f8dc4ed", "mtime": 1752463757, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\__init__.py", "plugin_data": null, "size": 1442, "suppressed": [], "version_id": "1.17.1"}