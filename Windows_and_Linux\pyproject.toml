[tool.ruff]
# Same as Black.
line-length = 120

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "myvenv",
]

# Assume Python 3.9+.
target-version = "py39"

[tool.ruff.lint]
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
select = ["E", "F", "W", "I", "N", "UP", "YTT", "S", "BLE", "FBT", "B", "A", "COM", "C4", "DTZ", "T10", "EM", "EXE", "ISC", "ICN", "G", "INP", "PIE", "T20", "PYI", "PT", "Q", "RSE", "RET", "SLF", "SIM", "TID", "TCH", "ARG", "PTH", "ERA", "PD", "PGH", "PL", "TRY", "NPY", "RUF"]
ignore = [
    "E501",  # Line too long (handled by black)
    "S101",  # Use of assert
    "T201",  # Print statements
    "PLR0913",  # Too many arguments
    "PLR0912",  # Too many branches
    "PLR0915",  # Too many statements
    "G004",  # Logging statement uses f-string
    "ERA001",  # Found commented-out code
    "PLC0415",  # Import should be at the top-level
    "PTH110",  # os.path.exists() should be replaced by Path.exists()
    "PTH118",  # os.path.join() should be replaced by Path with / operator
    "PTH120",  # os.path.dirname() should be replaced by Path.parent
    "N802",  # Function name should be lowercase (Qt methods)
    "N806",  # Variable should be lowercase (Qt variables)
    "N816",  # Variable in global scope should not be mixedCase
    "N801",  # Class name should use CapWords convention
    "N999",  # Invalid module name
    "BLE001",  # Do not catch blind exception
    "TRY400",  # Use logging.exception instead of logging.error
    "TRY401",  # Redundant exception object in logging.exception
    "G201",  # Use logging.exception instead of logging.error with exc_info
    "FBT002",  # Boolean default positional argument
    "FBT003",  # Boolean positional value in function call
    "SIM108",  # Use ternary operator instead of if-else-block
    "PLW0603",  # Using global statement is discouraged
    "E711",  # Comparison to None should be cond is not None
    "RET504",  # Unnecessary assignment before return
    "ARG002",  # Unused method argument
    "SLF001",  # Private member accessed
    "F821",  # Undefined name
    "B018",  # Found useless expression
    "PLR2004",  # Magic value used in comparison
    "RUF001",  # String contains ambiguous character
    "PGH003",  # Use specific rule codes when ignoring type issues
    "S310",  # Audit URL open for permitted schemes
    "E731",  # Do not assign a lambda expression, use a def
    "W293",  # Blank line contains whitespace
]

# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.black]
line-length = 120
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | myvenv
)/
'''

[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.9"
warn_return_any = false
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = false
disallow_untyped_decorators = false
no_implicit_optional = false
warn_redundant_casts = false
warn_unused_ignores = false
warn_no_return = false
warn_unreachable = false
strict_equality = false
ignore_missing_imports = true

# Exclude certain directories
exclude = [
    "build/",
    "dist/",
    "myvenv/",
    "__pycache__/",
]
