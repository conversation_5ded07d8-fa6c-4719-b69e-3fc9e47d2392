{"data_mtime": 1754415052, "dep_lines": [22, 23, 21, 22, 24, 21, 15, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26], "dep_prios": [10, 10, 10, 20, 5, 20, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["google.generativeai.types.caching_types", "google.generativeai.types.content_types", "google.generativeai.protos", "google.generativeai.types", "google.generativeai.client", "google.generativeai", "__future__", "datetime", "textwrap", "typing", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "google.ai", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.cache_service", "google.ai.generativelanguage_v1beta.services.cache_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.cache_service", "google.ai.generativelanguage_v1beta.types.cached_content", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.file", "google.generativeai.types.file_types"], "hash": "ba53bafa2b00cb584b04f2251cf67d73f00cc674", "id": "google.generativeai.caching", "ignore_all": true, "interface_hash": "e251c1434544f7cef160de29f9291183e75d96c8", "mtime": 1754404370, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\caching.py", "plugin_data": null, "size": 10795, "suppressed": ["google.protobuf"], "version_id": "1.17.1"}