{"data_mtime": 1754415047, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193, 196, 199, 202, 205, 208, 211, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.responses.tool", "openai.types.responses.response", "openai.types.responses.tool_param", "openai.types.responses.computer_tool", "openai.types.responses.function_tool", "openai.types.responses.response_item", "openai.types.responses.response_error", "openai.types.responses.response_usage", "openai.types.responses.parsed_response", "openai.types.responses.response_prompt", "openai.types.responses.response_status", "openai.types.responses.tool_choice_mcp", "openai.types.responses.web_search_tool", "openai.types.responses.file_search_tool", "openai.types.responses.tool_choice_types", "openai.types.responses.easy_input_message", "openai.types.responses.response_item_list", "openai.types.responses.computer_tool_param", "openai.types.responses.function_tool_param", "openai.types.responses.response_includable", "openai.types.responses.response_input_file", "openai.types.responses.response_input_item", "openai.types.responses.response_input_text", "openai.types.responses.tool_choice_options", "openai.types.responses.response_error_event", "openai.types.responses.response_input_image", "openai.types.responses.response_input_param", "openai.types.responses.response_output_item", "openai.types.responses.response_output_text", "openai.types.responses.response_text_config", "openai.types.responses.tool_choice_function", "openai.types.responses.response_failed_event", "openai.types.responses.response_prompt_param", "openai.types.responses.response_queued_event", "openai.types.responses.response_stream_event", "openai.types.responses.tool_choice_mcp_param", "openai.types.responses.web_search_tool_param", "openai.types.responses.file_search_tool_param", "openai.types.responses.input_item_list_params", "openai.types.responses.response_create_params", "openai.types.responses.response_created_event", "openai.types.responses.response_input_content", "openai.types.responses.response_output_message", "openai.types.responses.response_output_refusal", "openai.types.responses.response_reasoning_item", "openai.types.responses.tool_choice_types_param", "openai.types.responses.easy_input_message_param", "openai.types.responses.response_completed_event", "openai.types.responses.response_retrieve_params", "openai.types.responses.response_text_done_event", "openai.types.responses.response_audio_done_event", "openai.types.responses.response_incomplete_event", "openai.types.responses.response_input_file_param", "openai.types.responses.response_input_item_param", "openai.types.responses.response_input_text_param", "openai.types.responses.response_text_delta_event", "openai.types.responses.response_audio_delta_event", "openai.types.responses.response_in_progress_event", "openai.types.responses.response_input_image_param", "openai.types.responses.response_output_text_param", "openai.types.responses.response_text_config_param", "openai.types.responses.tool_choice_function_param", "openai.types.responses.response_computer_tool_call", "openai.types.responses.response_format_text_config", "openai.types.responses.response_function_tool_call", "openai.types.responses.response_input_message_item", "openai.types.responses.response_refusal_done_event", "openai.types.responses.response_function_web_search", "openai.types.responses.response_input_content_param", "openai.types.responses.response_refusal_delta_event", "openai.types.responses.response_output_message_param", "openai.types.responses.response_output_refusal_param", "openai.types.responses.response_reasoning_item_param", "openai.types.responses.response_file_search_tool_call", "openai.types.responses.response_mcp_call_failed_event", "openai.types.responses.response_output_item_done_event", "openai.types.responses.response_content_part_done_event", "openai.types.responses.response_function_tool_call_item", "openai.types.responses.response_output_item_added_event", "openai.types.responses.response_computer_tool_call_param", "openai.types.responses.response_content_part_added_event", "openai.types.responses.response_format_text_config_param", "openai.types.responses.response_function_tool_call_param", "openai.types.responses.response_mcp_call_completed_event", "openai.types.responses.response_function_web_search_param", "openai.types.responses.response_code_interpreter_tool_call", "openai.types.responses.response_input_message_content_list", "openai.types.responses.response_mcp_call_in_progress_event", "openai.types.responses.response_audio_transcript_done_event", "openai.types.responses.response_file_search_tool_call_param", "openai.types.responses.response_mcp_list_tools_failed_event", "openai.types.responses.response_audio_transcript_delta_event", "openai.types.responses.response_reasoning_summary_done_event", "openai.types.responses.response_mcp_call_arguments_done_event", "openai.types.responses.response_reasoning_summary_delta_event", "openai.types.responses.response_computer_tool_call_output_item", "openai.types.responses.response_format_text_json_schema_config", "openai.types.responses.response_function_tool_call_output_item", "openai.types.responses.response_image_gen_call_completed_event", "openai.types.responses.response_mcp_call_arguments_delta_event", "openai.types.responses.response_mcp_list_tools_completed_event", "openai.types.responses.response_image_gen_call_generating_event", "openai.types.responses.response_web_search_call_completed_event", "openai.types.responses.response_web_search_call_searching_event", "openai.types.responses.response_code_interpreter_tool_call_param", "openai.types.responses.response_file_search_call_completed_event", "openai.types.responses.response_file_search_call_searching_event", "openai.types.responses.response_image_gen_call_in_progress_event", "openai.types.responses.response_input_message_content_list_param", "openai.types.responses.response_mcp_list_tools_in_progress_event", "openai.types.responses.response_reasoning_summary_part_done_event", "openai.types.responses.response_reasoning_summary_text_done_event", "openai.types.responses.response_web_search_call_in_progress_event", "openai.types.responses.response_file_search_call_in_progress_event", "openai.types.responses.response_function_call_arguments_done_event", "openai.types.responses.response_image_gen_call_partial_image_event", "openai.types.responses.response_output_text_annotation_added_event", "openai.types.responses.response_reasoning_summary_part_added_event", "openai.types.responses.response_reasoning_summary_text_delta_event", "openai.types.responses.response_function_call_arguments_delta_event", "openai.types.responses.response_computer_tool_call_output_screenshot", "openai.types.responses.response_format_text_json_schema_config_param", "openai.types.responses.response_code_interpreter_call_code_done_event", "openai.types.responses.response_code_interpreter_call_completed_event", "openai.types.responses.response_code_interpreter_call_code_delta_event", "openai.types.responses.response_code_interpreter_call_in_progress_event", "openai.types.responses.response_code_interpreter_call_interpreting_event", "openai.types.responses.response_computer_tool_call_output_screenshot_param", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "cba8cbe01192c892f0faadcdee06136be5f551d5", "id": "openai.types.responses", "ignore_all": true, "interface_hash": "473a7d9ccd2e6d318339bf6cc47d20a6e61e5548", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\responses\\__init__.py", "plugin_data": null, "size": 13415, "suppressed": [], "version_id": "1.17.1"}