{"data_mtime": 1754415050, "dep_lines": [34, 32, 25, 32, 22, 22, 24, 37, 22, 23, 16, 17, 18, 19, 20, 23, 1, 1, 1, 1, 1, 1, 1, 1, 27, 28, 26, 28, 29, 30], "dep_prios": [5, 10, 5, 20, 10, 10, 10, 10, 20, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 5, 10, 5, 20, 10, 10], "dependencies": ["google.ai.generativelanguage_v1beta.services.generative_service.transports.base", "google.ai.generativelanguage_v1beta.types.generative_service", "google.auth.transport.grpc", "google.ai.generativelanguage_v1beta.types", "google.api_core.gapic_v1", "google.api_core.grpc_helpers", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "json", "logging", "pickle", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.auth._credentials_base", "google.auth.transport"], "hash": "feb910fb449c0ab44e0d502838437c6d277329b0", "id": "google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc", "ignore_all": true, "interface_hash": "ed733617af4f6e3c1b5d0cc13803e6ca3cc2f3ec", "mtime": 1752463757, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\grpc.py", "plugin_data": null, "size": 25075, "suppressed": ["google.protobuf.json_format", "google.protobuf.message", "google.longrunning", "google.protobuf", "grpc", "proto"], "version_id": "1.17.1"}