{"data_mtime": 1754415048, "dep_lines": [3, 11, 19, 27, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.resources.fine_tuning.jobs", "openai.resources.fine_tuning.alpha", "openai.resources.fine_tuning.checkpoints", "openai.resources.fine_tuning.fine_tuning", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "6612b5dba0d0e5622ecc9d99a4fc5b01067c549e", "id": "openai.resources.fine_tuning", "ignore_all": true, "interface_hash": "fccac01a9c71feafa2399b3af825d21b2c4f1849", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\fine_tuning\\__init__.py", "plugin_data": null, "size": 1597, "suppressed": [], "version_id": "1.17.1"}