{"data_mtime": 1754415047, "dep_lines": [10, 11, 12, 13, 14, 15, 16, 17, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai._types", "openai._utils", "openai._client", "openai._compat", "openai._models", "openai._streaming", "openai._exceptions", "openai._base_client", "__future__", "os", "inspect", "typing", "typing_extensions", "httpx", "builtins", "_frozen_importlib", "abc", "httpx._client", "httpx._config", "httpx._models", "httpx._urls", "openai._constants", "openai._utils._utils", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "df471e202860ab50f80b93223182ade0471bf6eb", "id": "openai.lib.azure", "ignore_all": true, "interface_hash": "6b9c861e5324a49ace434c3ff1dc9cbcf9597bea", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\azure.py", "plugin_data": null, "size": 26248, "suppressed": [], "version_id": "1.17.1"}