{"data_mtime": 1754415340, "dep_lines": [36, 37, 32, 34, 26, 32, 22, 23, 23, 23, 24, 25, 45, 22, 25, 30, 16, 17, 18, 19, 20, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 28], "dep_prios": [5, 5, 10, 10, 5, 20, 10, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["google.ai.generativelanguage_v1beta.services.permission_service.transports.base", "google.ai.generativelanguage_v1beta.services.permission_service.transports.rest_base", "google.ai.generativelanguage_v1beta.types.permission", "google.ai.generativelanguage_v1beta.types.permission_service", "google.auth.transport.requests", "google.ai.generativelanguage_v1beta.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.rest_helpers", "google.api_core.rest_streaming", "google.api_core.retry", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "requests.__version__", "dataclasses", "json", "logging", "typing", "warnings", "requests", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth.transport", "requests.sessions"], "hash": "94d21b52342ad2bc1c427fad2ba8f14a2d9cc38e", "id": "google.ai.generativelanguage_v1beta.services.permission_service.transports.rest", "ignore_all": true, "interface_hash": "9d6d289e855fe57efe3a3bc8ec9b90118cbf4c1c", "mtime": 1752463757, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\rest.py", "plugin_data": null, "size": 67683, "suppressed": ["google.longrunning", "google.protobuf"], "version_id": "1.17.1"}