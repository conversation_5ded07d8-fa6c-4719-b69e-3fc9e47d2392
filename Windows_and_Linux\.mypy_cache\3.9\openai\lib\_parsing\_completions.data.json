{".class": "MypyFile", "_fullname": "openai.lib._parsing._completions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ChatCompletion": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion.ChatCompletion", "kind": "Gdef"}, "ChatCompletionMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_message.ChatCompletionMessage", "kind": "Gdef"}, "ChatCompletionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam", "kind": "Gdef"}, "ContentFilterFinishReasonError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.ContentFilterFinishReasonError", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_message_tool_call.Function", "kind": "Gdef"}, "FunctionDefinition": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared_params.function_definition.FunctionDefinition", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LengthFinishReasonError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.LengthFinishReasonError", "kind": "Gdef"}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef"}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef"}, "PYDANTIC_V2": {".class": "SymbolTableNode", "cross_ref": "openai._compat.PYDANTIC_V2", "kind": "Gdef"}, "ParsedChatCompletion": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion", "kind": "Gdef"}, "ParsedChatCompletionMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletionMessage", "kind": "Gdef"}, "ParsedChoice": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChoice", "kind": "Gdef"}, "ParsedFunction": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_function_tool_call.ParsedFunction", "kind": "Gdef"}, "ParsedFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_function_tool_call.ParsedFunctionToolCall", "kind": "Gdef"}, "PydanticFunctionTool": {".class": "SymbolTableNode", "cross_ref": "openai.lib._tools.PydanticFunctionTool", "kind": "Gdef"}, "ResponseFormatParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_create_params.ResponseFormat", "kind": "Gdef"}, "ResponseFormatT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "name": "ResponseFormatT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._completions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._completions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._completions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._completions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._completions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._completions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_default_response_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai.lib._parsing._completions._default_response_format", "name": "_default_response_format", "setter_type": null, "type": {".class": "NoneType"}}}, "_parse_content": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["response_format", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions._parse_content", "name": "_parse_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["response_format", "content"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions._parse_content", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_content", "ret_type": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions._parse_content", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions._parse_content", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.assert_never", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "completion_create_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_create_params", "kind": "Gdef"}, "construct_type_unchecked": {".class": "SymbolTableNode", "cross_ref": "openai._models.construct_type_unchecked", "kind": "Gdef"}, "get_input_tool_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["input_tools", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.get_input_tool_by_name", "name": "get_input_tool_by_name", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["input_tools", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_input_tool_by_name", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_parseable_input": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 5], "arg_names": ["response_format", "input_tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.has_parseable_input", "name": "has_parseable_input", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["response_format", "input_tools"], "arg_types": [{".class": "UnionType", "items": ["builtins.type", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_parseable_input", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_rich_response_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.has_rich_response_format", "name": "has_rich_response_format", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response_format"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.has_rich_response_format", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_rich_response_format", "ret_type": "builtins.bool", "type_guard": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.has_rich_response_format", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.has_rich_response_format", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "is_basemodel_type": {".class": "SymbolTableNode", "cross_ref": "openai.lib._pydantic.is_basemodel_type", "kind": "Gdef"}, "is_dataclass_like_type": {".class": "SymbolTableNode", "cross_ref": "openai.lib._pydantic.is_dataclass_like_type", "kind": "Gdef"}, "is_dict": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_dict", "kind": "Gdef"}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef"}, "is_parseable_tool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.is_parseable_tool", "name": "is_parseable_tool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["input_tool"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_parseable_tool", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_response_format_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.is_response_format_param", "name": "is_response_format_param", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response_format"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_response_format_param", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "maybe_parse_content": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["response_format", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.maybe_parse_content", "name": "maybe_parse_content", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["response_format", "message"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.maybe_parse_content", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["openai.types.chat.chat_completion_message.ChatCompletionMessage", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletionMessage"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "maybe_parse_content", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.maybe_parse_content", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.maybe_parse_content", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "model_parse_json": {".class": "SymbolTableNode", "cross_ref": "openai._compat.model_parse_json", "kind": "Gdef"}, "parse_chat_completion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3], "arg_names": ["response_format", "input_tools", "chat_completion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.parse_chat_completion", "name": "parse_chat_completion", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3], "arg_names": ["response_format", "input_tools", "chat_completion"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.parse_chat_completion", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["openai.types.chat.chat_completion.ChatCompletion", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_chat_completion", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.parse_chat_completion", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.parse_chat_completion", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "parse_function_tool_arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["input_tools", "function"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.parse_function_tool_arguments", "name": "parse_function_tool_arguments", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["input_tools", "function"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["openai.types.chat.chat_completion_message_tool_call.Function", "openai.types.chat.parsed_function_tool_call.ParsedFunction"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_function_tool_arguments", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pydantic": {".class": "SymbolTableNode", "cross_ref": "pydantic", "kind": "Gdef"}, "solve_response_format_t": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.solve_response_format_t", "name": "solve_response_format_t", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response_format"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.solve_response_format_t", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "solve_response_format_t", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.solve_response_format_t", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib._parsing._completions.solve_response_format_t", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "to_strict_json_schema": {".class": "SymbolTableNode", "cross_ref": "openai.lib._pydantic.to_strict_json_schema", "kind": "Gdef"}, "type_to_response_format_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.type_to_response_format_param", "name": "type_to_response_format_param", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response_format"], "arg_types": [{".class": "UnionType", "items": ["builtins.type", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type_to_response_format_param", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_input_tools": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._completions.validate_input_tools", "name": "validate_input_tools", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["tools"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_input_tools", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\lib\\_parsing\\_completions.py"}