{"data_mtime": 1754415047, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.beta.threads.run", "openai.types.beta.threads.text", "openai.types.beta.threads.message", "openai.types.beta.threads.image_url", "openai.types.beta.threads.annotation", "openai.types.beta.threads.image_file", "openai.types.beta.threads.run_status", "openai.types.beta.threads.text_delta", "openai.types.beta.threads.message_delta", "openai.types.beta.threads.image_url_delta", "openai.types.beta.threads.image_url_param", "openai.types.beta.threads.message_content", "openai.types.beta.threads.message_deleted", "openai.types.beta.threads.run_list_params", "openai.types.beta.threads.annotation_delta", "openai.types.beta.threads.image_file_delta", "openai.types.beta.threads.image_file_param", "openai.types.beta.threads.text_delta_block", "openai.types.beta.threads.run_create_params", "openai.types.beta.threads.run_update_params", "openai.types.beta.threads.text_content_block", "openai.types.beta.threads.message_delta_event", "openai.types.beta.threads.message_list_params", "openai.types.beta.threads.refusal_delta_block", "openai.types.beta.threads.file_path_annotation", "openai.types.beta.threads.image_url_delta_block", "openai.types.beta.threads.message_content_delta", "openai.types.beta.threads.message_create_params", "openai.types.beta.threads.message_update_params", "openai.types.beta.threads.refusal_content_block", "openai.types.beta.threads.image_file_delta_block", "openai.types.beta.threads.image_url_content_block", "openai.types.beta.threads.file_citation_annotation", "openai.types.beta.threads.image_file_content_block", "openai.types.beta.threads.text_content_block_param", "openai.types.beta.threads.file_path_delta_annotation", "openai.types.beta.threads.message_content_part_param", "openai.types.beta.threads.image_url_content_block_param", "openai.types.beta.threads.file_citation_delta_annotation", "openai.types.beta.threads.image_file_content_block_param", "openai.types.beta.threads.run_submit_tool_outputs_params", "openai.types.beta.threads.required_action_function_tool_call", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "58e48e630244b8f8b799cc0a01dd06b957f2043f", "id": "openai.types.beta.threads", "ignore_all": true, "interface_hash": "c42fe4c5d217329beabdb8c2031591e847fee3d4", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\threads\\__init__.py", "plugin_data": null, "size": 3066, "suppressed": [], "version_id": "1.17.1"}