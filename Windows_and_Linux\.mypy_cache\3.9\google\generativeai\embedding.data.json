{".class": "MypyFile", "_fullname": "google.generativeai.embedding", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DEFAULT_EMB_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.embedding.DEFAULT_EMB_MODEL", "name": "DEFAULT_EMB_MODEL", "setter_type": null, "type": "builtins.str"}}, "EMBEDDING_MAX_BATCH_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.embedding.EMBEDDING_MAX_BATCH_SIZE", "name": "EMBEDDING_MAX_BATCH_SIZE", "setter_type": null, "type": "builtins.int"}}, "EmbeddingTaskType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.embedding.EmbeddingTaskType", "line": 34, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.generative_service.TaskType"}}, "EmbeddingTaskTypeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.embedding.EmbeddingTaskTypeOptions", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", "builtins.str", "google.ai.generativelanguage_v1beta.types.generative_service.TaskType"], "uses_pep604_syntax": false}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.embedding.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_EMBEDDING_TASK_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.generativeai.embedding._EMBEDDING_TASK_TYPE", "name": "_EMBEDDING_TASK_TYPE", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, "google.ai.generativelanguage_v1beta.types.generative_service.TaskType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.embedding.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.embedding.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.embedding.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.embedding.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.embedding.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.embedding.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_batched": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.embedding._batched", "name": "_batched", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "content_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types", "kind": "Gdef"}, "embed_content": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "google.generativeai.embedding.embed_content", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "google.generativeai.embedding.embed_content", "name": "embed_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "google.generativeai.embedding.embed_content", "name": "embed_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.embedding.embed_content", "name": "embed_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "google.generativeai.embedding.embed_content", "name": "embed_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.embedding.embed_content", "name": "embed_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "embed_content_async": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "google.generativeai.embedding.embed_content_async", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "google.generativeai.embedding.embed_content_async", "name": "embed_content_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "google.generativeai.embedding.embed_content_async", "name": "embed_content_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.embedding.embed_content_async", "name": "embed_content_async", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "google.generativeai.embedding.embed_content_async", "name": "embed_content_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.embedding.embed_content_async", "name": "embed_content_async", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.EmbeddingDict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["model", "content", "task_type", "title", "output_dimensionality", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.content_types.ContentType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_content_async", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.text_types.BatchEmbeddingDict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_default_generative_async_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_generative_async_client", "kind": "Gdef"}, "get_default_generative_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_generative_client", "kind": "Gdef"}, "glm": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage", "kind": "Gdef"}, "helper_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.helper_types", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "model_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef"}, "text_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.text_types", "kind": "Gdef"}, "to_task_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.embedding.to_task_type", "name": "to_task_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.embedding.EmbeddingTaskTypeOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_task_type", "ret_type": "google.ai.generativelanguage_v1beta.types.generative_service.TaskType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\google\\generativeai\\embedding.py"}