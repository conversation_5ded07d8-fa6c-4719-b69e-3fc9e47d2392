{"data_mtime": 1754415047, "dep_lines": [10, 11, 12, 13, 14, 15, 16, 17, 8, 9, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.evals.eval_api_error", "openai.types.responses.tool", "openai.types.shared.metadata", "openai.types.shared.reasoning_effort", "openai.types.responses.response_input_text", "openai.types.evals.create_eval_jsonl_run_data_source", "openai.types.responses.response_format_text_config", "openai.types.evals.create_eval_completions_run_data_source", "openai._utils", "openai._models", "typing", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "abc", "annotated_types", "openai.types.responses", "openai.types.responses.computer_tool", "openai.types.responses.file_search_tool", "openai.types.responses.function_tool", "openai.types.responses.response_format_text_json_schema_config", "openai.types.responses.web_search_tool", "openai.types.shared", "openai.types.shared.response_format_json_object", "openai.types.shared.response_format_text", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "re"], "hash": "5c8d5d027fa48acfa6b803192055779831cb7730", "id": "openai.types.evals.run_retrieve_response", "ignore_all": true, "interface_hash": "d264fa863ea70baa7f54b5fa0e9f2d8a6361eb1f", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\evals\\run_retrieve_response.py", "plugin_data": null, "size": 12998, "suppressed": [], "version_id": "1.17.1"}