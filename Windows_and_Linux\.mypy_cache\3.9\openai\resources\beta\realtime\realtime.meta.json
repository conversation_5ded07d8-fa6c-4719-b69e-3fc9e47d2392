{"data_mtime": 1754415047, "dep_lines": [14, 35, 35, 35, 40, 49, 50, 51, 52, 35, 48, 55, 56, 22, 23, 30, 31, 32, 33, 34, 58, 256, 3, 5, 6, 7, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 20, 20, 5, 5, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.realtime.sessions", "openai.types.beta.realtime.session_update_event_param", "openai.types.beta.realtime.response_create_event_param", "openai.types.beta.realtime.transcription_session_update_param", "openai.resources.beta.realtime.transcription_sessions", "openai.types.beta.realtime.realtime_client_event", "openai.types.beta.realtime.realtime_server_event", "openai.types.beta.realtime.conversation_item_param", "openai.types.beta.realtime.realtime_client_event_param", "openai.types.beta.realtime", "openai.types.websocket_connection_options", "websockets.sync.client", "websockets.asyncio.client", "openai._types", "openai._utils", "openai._compat", "openai._models", "openai._resource", "openai._exceptions", "openai._base_client", "openai._client", "websockets.exceptions", "__future__", "json", "logging", "types", "typing", "typing_extensions", "httpx", "pydantic", "builtins", "_frozen_importlib", "_socket", "_ssl", "abc", "asyncio", "asyncio.protocols", "httpx._urls", "openai._utils._utils", "openai.lib", "openai.lib.azure", "openai.types", "openai.types.beta", "openai.types.beta.realtime.conversation_created_event", "openai.types.beta.realtime.conversation_item_content_param", "openai.types.beta.realtime.conversation_item_create_event", "openai.types.beta.realtime.conversation_item_create_event_param", "openai.types.beta.realtime.conversation_item_created_event", "openai.types.beta.realtime.conversation_item_delete_event", "openai.types.beta.realtime.conversation_item_delete_event_param", "openai.types.beta.realtime.conversation_item_deleted_event", "openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event", "openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event", "openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event", "openai.types.beta.realtime.conversation_item_retrieve_event", "openai.types.beta.realtime.conversation_item_retrieve_event_param", "openai.types.beta.realtime.conversation_item_truncate_event", "openai.types.beta.realtime.conversation_item_truncate_event_param", "openai.types.beta.realtime.conversation_item_truncated_event", "openai.types.beta.realtime.conversation_item_with_reference_param", "openai.types.beta.realtime.error_event", "openai.types.beta.realtime.input_audio_buffer_append_event", "openai.types.beta.realtime.input_audio_buffer_append_event_param", "openai.types.beta.realtime.input_audio_buffer_clear_event", "openai.types.beta.realtime.input_audio_buffer_clear_event_param", "openai.types.beta.realtime.input_audio_buffer_cleared_event", "openai.types.beta.realtime.input_audio_buffer_commit_event", "openai.types.beta.realtime.input_audio_buffer_commit_event_param", "openai.types.beta.realtime.input_audio_buffer_committed_event", "openai.types.beta.realtime.input_audio_buffer_speech_started_event", "openai.types.beta.realtime.input_audio_buffer_speech_stopped_event", "openai.types.beta.realtime.rate_limits_updated_event", "openai.types.beta.realtime.response_audio_delta_event", "openai.types.beta.realtime.response_audio_done_event", "openai.types.beta.realtime.response_audio_transcript_delta_event", "openai.types.beta.realtime.response_audio_transcript_done_event", "openai.types.beta.realtime.response_cancel_event", "openai.types.beta.realtime.response_cancel_event_param", "openai.types.beta.realtime.response_content_part_added_event", "openai.types.beta.realtime.response_content_part_done_event", "openai.types.beta.realtime.response_create_event", "openai.types.beta.realtime.response_created_event", "openai.types.beta.realtime.response_done_event", "openai.types.beta.realtime.response_function_call_arguments_delta_event", "openai.types.beta.realtime.response_function_call_arguments_done_event", "openai.types.beta.realtime.response_output_item_added_event", "openai.types.beta.realtime.response_output_item_done_event", "openai.types.beta.realtime.response_text_delta_event", "openai.types.beta.realtime.response_text_done_event", "openai.types.beta.realtime.session_created_event", "openai.types.beta.realtime.session_update_event", "openai.types.beta.realtime.session_updated_event", "openai.types.beta.realtime.transcription_session_update", "openai.types.beta.realtime.transcription_session_updated_event", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "socket", "ssl", "websockets", "websockets.asyncio", "websockets.asyncio.connection", "websockets.datastructures", "websockets.extensions", "websockets.extensions.base", "websockets.frames", "websockets.sync", "websockets.sync.connection", "websockets.typing"], "hash": "07fa5ccd6f076f07c00dc19aade2078f8f4913c3", "id": "openai.resources.beta.realtime.realtime", "ignore_all": true, "interface_hash": "5b7cb2c24829e1ca4a73065b127efcb85981c5a0", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\realtime.py", "plugin_data": null, "size": 43470, "suppressed": [], "version_id": "1.17.1"}