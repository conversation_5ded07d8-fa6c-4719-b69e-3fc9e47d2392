{".class": "MypyFile", "_fullname": "openai.types.beta", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Assistant": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant.Assistant", "kind": "Gdef"}, "AssistantCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_create_params.AssistantCreateParams", "kind": "Gdef"}, "AssistantDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_deleted.AssistantDeleted", "kind": "Gdef"}, "AssistantListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_list_params.AssistantListParams", "kind": "Gdef"}, "AssistantResponseFormatOption": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_response_format_option.AssistantResponseFormatOption", "kind": "Gdef"}, "AssistantResponseFormatOptionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_response_format_option_param.AssistantResponseFormatOptionParam", "kind": "Gdef"}, "AssistantStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent", "kind": "Gdef"}, "AssistantTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool.AssistantTool", "kind": "Gdef"}, "AssistantToolChoice": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice.AssistantToolChoice", "kind": "Gdef"}, "AssistantToolChoiceFunction": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice_function.AssistantToolChoiceFunction", "kind": "Gdef"}, "AssistantToolChoiceFunctionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice_function_param.AssistantToolChoiceFunctionParam", "kind": "Gdef"}, "AssistantToolChoiceOption": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice_option.AssistantToolChoiceOption", "kind": "Gdef"}, "AssistantToolChoiceOptionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice_option_param.AssistantToolChoiceOptionParam", "kind": "Gdef"}, "AssistantToolChoiceParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_choice_param.AssistantToolChoiceParam", "kind": "Gdef"}, "AssistantToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_tool_param.AssistantToolParam", "kind": "Gdef"}, "AssistantUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_update_params.AssistantUpdateParams", "kind": "Gdef"}, "CodeInterpreterTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.code_interpreter_tool.CodeInterpreterTool", "kind": "Gdef"}, "CodeInterpreterToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.code_interpreter_tool_param.CodeInterpreterToolParam", "kind": "Gdef"}, "FileSearchTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.file_search_tool.FileSearchTool", "kind": "Gdef"}, "FileSearchToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.file_search_tool_param.FileSearchToolParam", "kind": "Gdef"}, "FunctionTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.function_tool.FunctionTool", "kind": "Gdef"}, "FunctionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.function_tool_param.FunctionToolParam", "kind": "Gdef"}, "Thread": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread.Thread", "kind": "Gdef"}, "ThreadCreateAndRunParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_create_and_run_params.ThreadCreateAndRunParams", "kind": "Gdef"}, "ThreadCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_create_params.ThreadCreateParams", "kind": "Gdef"}, "ThreadDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_deleted.ThreadDeleted", "kind": "Gdef"}, "ThreadUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.thread_update_params.ThreadUpdateParams", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\beta\\__init__.py"}