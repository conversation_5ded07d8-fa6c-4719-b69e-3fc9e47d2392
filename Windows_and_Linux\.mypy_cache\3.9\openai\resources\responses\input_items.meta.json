{"data_mtime": 1754415047, "dep_lines": [18, 19, 20, 18, 10, 11, 12, 13, 14, 15, 16, 17, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.responses.input_item_list_params", "openai.types.responses.response_item", "openai.types.responses.response_includable", "openai.types.responses", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types", "openai.types.responses.response_code_interpreter_tool_call", "openai.types.responses.response_computer_tool_call", "openai.types.responses.response_computer_tool_call_output_item", "openai.types.responses.response_file_search_tool_call", "openai.types.responses.response_function_tool_call", "openai.types.responses.response_function_tool_call_item", "openai.types.responses.response_function_tool_call_output_item", "openai.types.responses.response_function_web_search", "openai.types.responses.response_input_message_item", "openai.types.responses.response_output_message", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "1ed55bd92d322ede399fcfed71a1024ccda30996", "id": "openai.resources.responses.input_items", "ignore_all": true, "interface_hash": "986241413d85a55b7a95cd36d7dc2eb15ced7373", "mtime": 1754404358, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\responses\\input_items.py", "plugin_data": null, "size": 9150, "suppressed": [], "version_id": "1.17.1"}