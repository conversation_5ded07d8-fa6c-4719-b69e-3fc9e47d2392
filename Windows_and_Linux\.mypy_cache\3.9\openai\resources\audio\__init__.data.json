{".class": "MypyFile", "_fullname": "openai.resources.audio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAudio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudio", "kind": "Gdef"}, "AsyncAudioWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "kind": "Gdef"}, "AsyncAudioWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "kind": "Gdef"}, "AsyncSpeech": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.AsyncSpeech", "kind": "Gdef"}, "AsyncSpeechWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.AsyncSpeechWithRawResponse", "kind": "Gdef"}, "AsyncSpeechWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.AsyncSpeechWithStreamingResponse", "kind": "Gdef"}, "AsyncTranscriptions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.AsyncTranscriptions", "kind": "Gdef"}, "AsyncTranscriptionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithRawResponse", "kind": "Gdef"}, "AsyncTranscriptionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithStreamingResponse", "kind": "Gdef"}, "AsyncTranslations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.AsyncTranslations", "kind": "Gdef"}, "AsyncTranslationsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "kind": "Gdef"}, "AsyncTranslationsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "kind": "Gdef"}, "Audio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.Audio", "kind": "Gdef"}, "AudioWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AudioWithRawResponse", "kind": "Gdef"}, "AudioWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AudioWithStreamingResponse", "kind": "Gdef"}, "Speech": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.Speech", "kind": "Gdef"}, "SpeechWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.SpeechWithRawResponse", "kind": "Gdef"}, "SpeechWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.SpeechWithStreamingResponse", "kind": "Gdef"}, "Transcriptions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.Transcriptions", "kind": "Gdef"}, "TranscriptionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.TranscriptionsWithRawResponse", "kind": "Gdef"}, "TranscriptionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.TranscriptionsWithStreamingResponse", "kind": "Gdef"}, "Translations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.Translations", "kind": "Gdef"}, "TranslationsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.TranslationsWithRawResponse", "kind": "Gdef"}, "TranslationsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.audio.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\audio\\__init__.py"}