{".class": "MypyFile", "_fullname": "openai.types.audio", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "SpeechCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.speech_create_params.SpeechCreateParams", "kind": "Gdef"}, "SpeechModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.speech_model.SpeechModel", "kind": "Gdef"}, "Transcription": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription.Transcription", "kind": "Gdef"}, "TranscriptionCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_create_params.TranscriptionCreateParams", "kind": "Gdef"}, "TranscriptionCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_create_response.TranscriptionCreateResponse", "kind": "Gdef"}, "TranscriptionInclude": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_include.TranscriptionInclude", "kind": "Gdef"}, "TranscriptionSegment": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_segment.TranscriptionSegment", "kind": "Gdef"}, "TranscriptionStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_stream_event.TranscriptionStreamEvent", "kind": "Gdef"}, "TranscriptionTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_text_delta_event.TranscriptionTextDeltaEvent", "kind": "Gdef"}, "TranscriptionTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_text_done_event.TranscriptionTextDoneEvent", "kind": "Gdef"}, "TranscriptionVerbose": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_verbose.TranscriptionVerbose", "kind": "Gdef"}, "TranscriptionWord": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.transcription_word.TranscriptionWord", "kind": "Gdef"}, "Translation": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation.Translation", "kind": "Gdef"}, "TranslationCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation_create_params.TranslationCreateParams", "kind": "Gdef"}, "TranslationCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation_create_response.TranslationCreateResponse", "kind": "Gdef"}, "TranslationVerbose": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation_verbose.TranslationVerbose", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.audio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\audio\\__init__.py"}