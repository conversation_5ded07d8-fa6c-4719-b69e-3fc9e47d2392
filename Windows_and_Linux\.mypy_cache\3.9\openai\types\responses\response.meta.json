{"data_mtime": 1754415047, "dep_lines": [6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 7, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.responses.tool", "openai.types.responses.response_error", "openai.types.responses.response_usage", "openai.types.responses.response_prompt", "openai.types.responses.response_status", "openai.types.responses.tool_choice_mcp", "openai.types.shared.metadata", "openai.types.shared.reasoning", "openai.types.responses.tool_choice_types", "openai.types.responses.response_input_item", "openai.types.responses.tool_choice_options", "openai.types.responses.response_output_item", "openai.types.responses.response_text_config", "openai.types.responses.tool_choice_function", "openai.types.shared.responses_model", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "openai.types.responses.computer_tool", "openai.types.responses.easy_input_message", "openai.types.responses.file_search_tool", "openai.types.responses.function_tool", "openai.types.responses.response_code_interpreter_tool_call", "openai.types.responses.response_computer_tool_call", "openai.types.responses.response_file_search_tool_call", "openai.types.responses.response_function_tool_call", "openai.types.responses.response_function_web_search", "openai.types.responses.response_output_message", "openai.types.responses.response_reasoning_item", "openai.types.responses.web_search_tool", "openai.types.shared", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "d6a489552f71def569073a12697b114ba4fe776b", "id": "openai.types.responses.response", "ignore_all": true, "interface_hash": "bf7e58b09acb37e83984b5b14c9a42282f4171ae", "mtime": 1754404359, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\types\\responses\\response.py", "plugin_data": null, "size": 10819, "suppressed": [], "version_id": "1.17.1"}