{".class": "MypyFile", "_fullname": "openai.resources.evals", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncEvals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvals", "kind": "Gdef"}, "AsyncEvalsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "kind": "Gdef"}, "AsyncEvalsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "kind": "Gdef"}, "AsyncRuns": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRuns", "kind": "Gdef"}, "AsyncRunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRunsWithRawResponse", "kind": "Gdef"}, "AsyncRunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRunsWithStreamingResponse", "kind": "Gdef"}, "Evals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.Evals", "kind": "Gdef"}, "EvalsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.EvalsWithRawResponse", "kind": "Gdef"}, "EvalsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.EvalsWithStreamingResponse", "kind": "Gdef"}, "Runs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.Runs", "kind": "Gdef"}, "RunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.RunsWithRawResponse", "kind": "Gdef"}, "RunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.RunsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.evals.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\openai\\resources\\evals\\__init__.py"}